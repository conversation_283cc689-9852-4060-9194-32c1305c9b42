import appConstant from '@/config/appConstant.js'
import apis from './config'
import upms from './upms'
import battery from './battery'
import device from './device'
import fullscreen from "./fullscreen";
import strategy from "./strategy";
import vpp from "./vpp";
import user from "./user"
import car from "./car"
import power from "./power"

for (const key in upms) {
    upms[key] = '/admin/' + upms[key]
}
for (const key in device) {
    device[key] = '/' + device[key]
}
for (const key in strategy) {
    strategy[key] = '/' + strategy[key]
}
for (const key in vpp) {
    vpp[key] = '/' + vpp[key]
}
for (const key in battery) {
    battery[key] = '/es/' + battery[key]
}
for (const key in fullscreen) {
    fullscreen[key] = "/" + fullscreen[key];
}
for (const key in user) {
    user[key] = "/" + user[key];
}
// for (const key in car) {
//     car[key] = car[key];
// }
for (const key in power) {
    power[key] = "/" + power[key];
}
let allApis = { ...apis, ...upms, ...battery, ...fullscreen, ...device, ...strategy, ...vpp, ...user, ...car ,...power}
let baseUrl = ''

if (process.env.NODE_ENV == 'development') {
    // baseUrl = 'https://outgatewaysit.mingwork.com'
    baseUrl = 'https://ems-api-beta.ssnj.com'
} else if (process.env.NODE_ENV == 'testing') {
    // baseUrl = "https://gateway-es-beta.mingwork.com:8443";
    baseUrl = 'https://ems-api-beta.ssnj.com'
} else if (process.env.NODE_ENV == 'production') {
    // baseUrl = appConstant.proApiBaseUrl
    baseUrl = 'https://ems-api.ssnj.com'
}

let needBaseUrl = url => {
    return !(
        url.startsWith('//') ||
        url.startsWith('http') ||
        url.startsWith('https')
    )
}
let getBaseUrl = url => {
    if (url.startsWith('/enterprise-websocket')) {
        let arr = baseUrl.split('://')
        return 'wss://' + arr[1]
    } else {
        return baseUrl
    }
}

function foreachApis(apis) {
    for (const key in apis) {
        let value = apis[key]
        if (typeof value == 'object') {
            foreachApis(value)
        } else {
            apis[key] = needBaseUrl(value) ? getBaseUrl(value) + value : value
        }
    }
}
foreachApis(allApis)
export default allApis
