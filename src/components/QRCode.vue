<template>
    <div>
        <div v-if="type === 'WECHAT'">
            <div class="mt-8">
                <div class="relative">
                    <div class="flex justify-center relative z-10">
                        <img :src="uri" alt="" width="200" />
                    </div>
                </div>
                <div class="px-3">
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-4"
                    >
                        微信扫码绑定
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        绑定完成后，即可访问微信小程序，在线查看设备运行状态与数据
                    </div>
                </div>
            </div>
        </div>
        <div v-if="type === 'DING_TALK'">
            <div class="mt-8">
                <div class="relative">
                    <div class="flex justify-center relative z-10">
                        <vue-qr
                            :logoSrc="src2"
                            :text="url"
                            :callback="test"
                            :size="200"
                        ></vue-qr>
                    </div>
                </div>
                <div class="px-3">
                    <div
                        class="text-center text-primary-text dark:text-80-dark mb-4"
                    >
                        钉钉扫码绑定
                    </div>
                    <div
                        class="text-center text-primary-text dark:text-80-dark"
                    >
                        绑定完成后，即可访问钉钉小程序
                        <br />
                        在线查看设备运行状态与数据。
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            // src: 'https://www.baidu.com/img/bd_logo1.png',
            src2: require('@/assets/login/icon.png'),
            url: `dingtalk://dingtalkclient/action/open_micro_app?appId=129525&page=pages%2Fbind%2Fbinding%3FuserId%3D${this.$store.state.user.userInfoData.userId}`,
        }
    },

    props: {
        type: {
            type: String,
            default: '',
        },
        uri: {
            type: String,
            default: '',
        },
    },
    methods: {
        test() {},
    },
}
</script>

<style lang="less" scoped></style>
