<template>
    <div class="mw-table">
        <a-config-provider
            :transformCellText="({ text }) => (text === '' ? '-' : text)"
        >
            <template #renderEmpty>
                <empty-data :description="$t('zanwushuju')">
                    <slot name="empty"></slot>
                </empty-data>
            </template>
            <a-table :pagination="false" v-bind="$attrs">
                <template
                    v-for="(index, name) in $slots"
                    v-slot:[name]="slotProps"
                >
                    <slot :name="name" v-bind="slotProps" />
                </template>
            </a-table>
        </a-config-provider>
        <a-pagination
            v-if="hasPage"
            size="small"
            style="text-align: right"
            :current="pageConfig.paginationProps.current"
            :total="pageConfig.paginationProps.total"
            :pageSize="pageConfig.paginationProps.pageSize"
            @change="pageConfig.changePage"
            @showSizeChange="pageConfig.changePage"
            show-size-changer
            show-quick-jumper
        />
    </div>
</template>
<script>
import { computed, toRefs, useSlots } from 'vue'
export default {
    props: {
        hasPage: { type: Boolean, default: false },
        pageConfig: {
            type: Object,
            default: () => ({}),
        },
    },
    setup(props, { emit }) {},
}
</script>
<style lang="less" scoped>
.mw-table {
    :deep(.ant-table-thead > tr > th) {
        background: none;
        font-weight: 400;
        padding: 12px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        // color: theme('colors.secondar-text');
    }
    :deep(.ant-table-tbody > tr > td) {
        padding: 12px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        // color: theme('colors.secondar-text');
    }
    :deep(.ant-table-tbody) {
        > tr:hover:not(.ant-table-expanded-row) > td,
        .ant-table-row-hover,
        .ant-table-row-hover > td {
            background: var(--table-hover-bg) !important;
        }
    }

    :deep(.ant-pagination) {
        margin: 12px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .ant-pagination-prev,
        .ant-pagination-next {
            line-height: 32px;
            height: 32px;
            min-width: 32px;
            .ant-pagination-item-link {
                font-size: 12px;
            }
        }

        .ant-pagination-item {
            min-width: 32px;
            height: 32px;
            margin-right: 8px;
            line-height: 30px;
            font-size: 14px;
            &:hover {
                a {
                    color: var(--themeColor);
                }
            }
        }

        .ant-pagination-item-active {
            border-color: var(--themeColor);
            background: transparent;
            a {
                color: var(--themeColor);
            }
        }

        .ant-pagination-options {
            margin-left: 16px;
            display: flex;
            align-items: center;
            .ant-select-selector {
                padding: 0 11px;
                height: 32px;
                font-size: 14px;
                .ant-select-selection-item {
                    padding-right: 18px;
                    height: 32px;
                    line-height: 30px;
                }
            }

            .ant-select {
                &:not(.ant-select-disabled) {
                    &:hover {
                        .ant-select-selector {
                            border-color: var(--themeColor);
                        }
                    }
                }
            }

            .ant-select-arrow {
                right: 11px;
                width: 12px;
                height: 12px;
                margin-top: -6px;
                font-size: 12px;
            }

            .ant-select-item {
                padding: 5px 12px;
                line-height: 22px;
                font-size: 14px;
                min-height: 32px;
                .ant-select-item-option-content {
                    font-size: 14px;
                }
            }

            .ant-select-focused {
                .ant-select-selector {
                    border-color: var(--themeColor);
                    box-shadow: none !important;
                }
            }

            .ant-pagination-options-quick-jumper {
                height: 32px;
                line-height: 32px;
                font-size: 14px;
                input {
                    padding: 4px 11px;
                    width: 44px;
                    font-size: 14px;
                    &:hover {
                        border-color: var(--themeColor);
                    }

                    &:focus {
                        border-color: var(--themeColor);
                        box-shadow: none;
                    }
                }
            }
        }
    }
}
</style>
