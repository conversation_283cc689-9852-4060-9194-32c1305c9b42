<template>
    <div class="inline-flex">
        <div class="num" :class="numClassName">
            {{ formattedPrice.number }}
        </div>
        <div class="unit" :class="unitClassName">
            {{ formattedPrice.unit }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import i18n from '@/lang.js'

const { t, locale } = i18n.global

const props = defineProps({
    price: {
        type: Number,
        required: true,
    },
    numSize: {
        type: [String, Number],
        default: 14,
    },
    unitSize: {
        type: [String, Number],
        default: 14,
    },
    numClassName: {
        type: String,
        default: '',
    },
    unitClassName: {
        type: String,
        default: '',
    },
})

const formattedPrice = computed(() => {
    const num = props.price
    if (locale.value === 'en') {
        if (num >= 1000) {
            return {
                number: (num / 1000).toFixed(1),
                unit: 'k',
            }
        } else {
            return {
                number: num.toString(),
                unit: '',
            }
        }
    } else if (locale.value === 'zh') {
        if (num >= 10000) {
            return {
                number: (num / 10000).toFixed(1),
                unit: '万元',
            }
        } else {
            return {
                number: num.toString(),
                unit: '元',
            }
        }
    } else {
        return {
            number: num.toString(),
            unit: '',
        }
    }
})
</script>

<style lang="less" scoped>
.inline-flex {
    display: inline-flex;
    align-items: baseline;
}

.unit {
    margin-left: 2px;
}
</style>
