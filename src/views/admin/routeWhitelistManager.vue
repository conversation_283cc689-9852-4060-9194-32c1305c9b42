<template>
    <div class="whitelist-manager">
        <h2>路由白名单管理</h2>

        <!-- 当前白名单展示 -->
        <div class="whitelist-display">
            <h3>当前白名单配置</h3>

            <div class="category-tabs">
                <button
                    v-for="category in categories"
                    :key="category.key"
                    @click="activeCategory = category.key"
                    :class="{ active: activeCategory === category.key }"
                    class="tab-btn"
                >
                    {{ category.label }}
                </button>
            </div>

            <div class="route-list">
                <div
                    v-for="route in currentCategoryRoutes"
                    :key="route"
                    class="route-item"
                >
                    <span class="route-path">{{ route }}</span>
                    <button
                        @click="removeRoute(route)"
                        class="btn-remove"
                        title="移除此路由"
                    >
                        ×
                    </button>
                </div>

                <div
                    v-if="currentCategoryRoutes.length === 0"
                    class="empty-state"
                >
                    暂无路由
                </div>
            </div>
        </div>

        <!-- 添加新路由 -->
        <div class="add-route-section">
            <h3>添加新路由</h3>

            <div class="add-form">
                <div class="form-group">
                    <label>路由路径:</label>
                    <input
                        v-model="newRoute"
                        type="text"
                        placeholder="例如: /api/public"
                        class="route-input"
                        @keyup.enter="addRoute"
                    />
                </div>

                <div class="form-group">
                    <label>分类:</label>
                    <select v-model="newRouteCategory" class="category-select">
                        <option value="basic">基础页面</option>
                        <option value="public">公开页面</option>
                        <option value="miniProgram">小程序页面</option>
                    </select>
                </div>

                <button @click="addRoute" class="btn-add">添加路由</button>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-operations">
            <h3>批量操作</h3>

            <div class="batch-buttons">
                <button @click="exportConfig" class="btn-export">
                    导出配置
                </button>
                <button @click="importConfig" class="btn-import">
                    导入配置
                </button>
                <button @click="resetToDefault" class="btn-reset">
                    重置为默认
                </button>
            </div>

            <div class="import-area" v-if="showImportArea">
                <textarea
                    v-model="importData"
                    placeholder="粘贴配置JSON数据..."
                    class="import-textarea"
                ></textarea>
                <div class="import-actions">
                    <button @click="confirmImport" class="btn-confirm">
                        确认导入
                    </button>
                    <button @click="cancelImport" class="btn-cancel">
                        取消
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h3>路由测试</h3>

            <div class="test-form">
                <input
                    v-model="testRoute"
                    type="text"
                    placeholder="输入路由路径进行测试"
                    class="test-input"
                />
                <button @click="testRouteAccess" class="btn-test">测试</button>
            </div>

            <div v-if="testResult" class="test-result" :class="testResult.type">
                <strong>测试结果:</strong> {{ testResult.message }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
    getWhitelistConfig,
    addToWhitelist,
    removeFromWhitelist,
    resetWhitelist,
    isRouteInWhitelist,
} from '@/router/whitelist.js'

// 响应式数据
const activeCategory = ref('basic')
const newRoute = ref('')
const newRouteCategory = ref('public')
const testRoute = ref('')
const testResult = ref(null)
const showImportArea = ref(false)
const importData = ref('')
const whitelistConfig = ref({})

// 分类配置
const categories = [
    { key: 'basic', label: '基础页面' },
    { key: 'public', label: '公开页面' },
    { key: 'miniProgram', label: '小程序页面' },
    { key: 'all', label: '全部路由' },
]

// 当前分类的路由列表
const currentCategoryRoutes = computed(() => {
    return whitelistConfig.value[activeCategory.value] || []
})

// 刷新白名单配置
const refreshConfig = () => {
    whitelistConfig.value = getWhitelistConfig()
}

// 添加路由
const addRoute = () => {
    if (!newRoute.value.trim()) {
        alert('请输入路由路径')
        return
    }

    if (!newRoute.value.startsWith('/')) {
        alert('路由路径必须以 / 开头')
        return
    }

    try {
        addToWhitelist(newRoute.value.trim(), newRouteCategory.value)
        refreshConfig()
        newRoute.value = ''
        alert('路由添加成功')
    } catch (error) {
        alert('添加失败: ' + error.message)
    }
}

// 移除路由
const removeRoute = (route) => {
    if (confirm(`确定要移除路由 "${route}" 吗？`)) {
        try {
            removeFromWhitelist(route)
            refreshConfig()
            alert('路由移除成功')
        } catch (error) {
            alert('移除失败: ' + error.message)
        }
    }
}

// 导出配置
const exportConfig = () => {
    const config = getWhitelistConfig()
    const dataStr = JSON.stringify(config, null, 2)

    // 创建下载链接
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'route-whitelist-config.json'
    link.click()
    URL.revokeObjectURL(url)
}

// 显示导入区域
const importConfig = () => {
    showImportArea.value = true
}

// 确认导入
const confirmImport = () => {
    try {
        const config = JSON.parse(importData.value)

        if (!config || typeof config !== 'object') {
            throw new Error('配置格式不正确')
        }

        // 这里可以添加更详细的验证逻辑
        console.log('导入配置:', config)
        alert('配置导入成功（注意：实际项目中需要实现导入逻辑）')

        cancelImport()
    } catch (error) {
        alert('导入失败: ' + error.message)
    }
}

// 取消导入
const cancelImport = () => {
    showImportArea.value = false
    importData.value = ''
}

// 重置为默认配置
const resetToDefault = () => {
    if (confirm('确定要重置为默认配置吗？这将清除所有自定义设置。')) {
        try {
            resetWhitelist()
            refreshConfig()
            alert('已重置为默认配置')
        } catch (error) {
            alert('重置失败: ' + error.message)
        }
    }
}

// 测试路由访问
const testRouteAccess = () => {
    if (!testRoute.value.trim()) {
        alert('请输入要测试的路由路径')
        return
    }

    const isInWhitelist = isRouteInWhitelist(testRoute.value.trim())

    testResult.value = {
        type: isInWhitelist ? 'success' : 'error',
        message: isInWhitelist
            ? '该路由在白名单中，无需token验证'
            : '该路由不在白名单中，需要token验证',
    }
}

// 页面挂载时刷新配置
onMounted(() => {
    refreshConfig()
})
</script>

<style lang="scss" scoped>
.whitelist-manager {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        sans-serif;

    h2,
    h3 {
        color: #333;
        margin-bottom: 20px;
    }

    h2 {
        text-align: center;
        font-size: 28px;
        margin-bottom: 30px;
    }

    h3 {
        font-size: 20px;
        border-bottom: 2px solid #3edacd;
        padding-bottom: 8px;
    }
}

.whitelist-display {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    .tab-btn {
        padding: 8px 16px;
        border: 1px solid #ddd;
        background: #f5f5f5;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background: #e9ecef;
        }

        &.active {
            background: #3edacd;
            color: white;
            border-color: #3edacd;
        }
    }
}

.route-list {
    max-height: 300px;
    overflow-y: auto;

    .route-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        margin-bottom: 8px;
        background: #f9f9f9;

        .route-path {
            font-family: 'Courier New', monospace;
            color: #333;
        }

        .btn-remove {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;

            &:hover {
                background: #c82333;
            }
        }
    }

    .empty-state {
        text-align: center;
        color: #999;
        padding: 40px;
        font-style: italic;
    }
}

.add-route-section,
.batch-operations,
.test-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.add-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;

    .form-group {
        display: flex;
        flex-direction: column;

        label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .route-input {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;

            &:focus {
                outline: none;
                border-color: #3edacd;
            }
        }

        .category-select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
    }

    .btn-add {
        padding: 8px 16px;
        background: #3edacd;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: #35c5b8;
        }
    }
}

.batch-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;

        &.btn-export {
            background: #28a745;
            color: white;

            &:hover {
                background: #218838;
            }
        }

        &.btn-import {
            background: #17a2b8;
            color: white;

            &:hover {
                background: #138496;
            }
        }

        &.btn-reset {
            background: #dc3545;
            color: white;

            &:hover {
                background: #c82333;
            }
        }
    }
}

.import-area {
    .import-textarea {
        width: 100%;
        height: 150px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        resize: vertical;
    }

    .import-actions {
        margin-top: 10px;
        display: flex;
        gap: 10px;

        .btn-confirm {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;

            &:hover {
                background: #218838;
            }
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;

            &:hover {
                background: #5a6268;
            }
        }
    }
}

.test-form {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;

    .test-input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;

        &:focus {
            outline: none;
            border-color: #3edacd;
        }
    }

    .btn-test {
        padding: 8px 16px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: #0056b3;
        }
    }
}

.test-result {
    padding: 10px;
    border-radius: 4px;

    &.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    &.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
}

@media (max-width: 768px) {
    .add-form {
        flex-direction: column;
        align-items: stretch;

        .form-group .route-input {
            width: 100%;
        }
    }

    .batch-buttons {
        flex-direction: column;
    }

    .test-form {
        flex-direction: column;
    }
}
</style>
