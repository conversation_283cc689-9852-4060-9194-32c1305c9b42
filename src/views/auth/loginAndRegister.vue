<template>
    <div
        class="video-box"
        :style="{ 'background-image': 'url(' + bgImg.loginBanner + ')' }"
    ></div>
    <div
        class="w-full h-screen flex flex-col justify-between items-center box absolute p-8"
    >
        <!-- w-full login-box flex justify-end -->
        <div class="absolute loginIn">
            <div>
                <div class="bg-ff dark:bg-ff-dark login-content">
                    <!-- <div class="login-title">登录后台</div> -->
                    <router-view></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { reactive, toRefs, computed } from 'vue'
import { useStore } from 'vuex'
export default {
    setup() {
        // const state = reactive({})
        // return { ...toRefs(state) }
        const state = useStore()
        const bgImg = computed(() => {
            if (
                state.getters['user/getConfigData'] &&
                state.getters['user/getConfigData'].loginBanner
            ) {
                return state.getters['user/getConfigData']
            } else {
                return {
                    loginBanner: require('@/assets/login/login-bg.jpg'),
                }
            }
        })
        return {
            bgImg,
        }
    },
}
</script>
<style lang="less" scoped>
.video-box {
    position: relative;
    height: 100vh;
    /*进行视频裁剪*/
    overflow: hidden;
    min-width: 1400px;
    min-height: 760px;
    // background-image: url('../../assets/login/login-wanjiale.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    z-index: 2;
}

.loginIn {
    top: 17%;
    right: 12%;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(131, 176, 193, 0.4);
    z-index: 10;
    .login-content {
        width: 480px;
        height: 540px;
        padding: 38px 42px;
    }
}

.login-title {
    height: 22px;
    font-size: 16px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: rgba(34, 34, 34, 0.65);
    line-height: 22px;
}

.video-box .video-background {
    position: absolute;
    left: 50%;
    top: 50%;
    /*保证视频内容始终居中*/
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    /*保证视频充满屏幕*/
    object-fit: cover;
    min-height: 800px;
}

// .box {
//   background: url("../../assets/login/ssnj.jpg") no-repeat center / cover;
// }
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88px;
    line-height: 88px;

    // width: 100%;
    .menu {
        // width: 1200px;
        position: relative;
        height: 100%;
    }
}

.login-box {
    width: 1440px;
    // height: calc(~"100vh - 160px");
    // height: 540px;
    margin: auto;
    // overflow: hidden;
    border-radius: 8px;
}
</style>
