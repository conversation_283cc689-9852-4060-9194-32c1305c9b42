<template>
    <div class="add-user-test">
        <h2>Add User Component Test</h2>
        
        <div class="test-controls">
            <el-form :model="testForm" label-width="120px">
                <el-form-item label="Test Mode:">
                    <el-radio-group v-model="testMode">
                        <el-radio label="add">Add User</el-radio>
                        <el-radio label="edit">Edit User</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="testMode === 'edit'" label="Edit Data:">
                    <el-button @click="setEditData">Set Edit Data</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="openAddUser">Open Add User Dialog</el-button>
                    <el-button @click="testSubmit" style="margin-left: 10px;">Test Submit</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="output-display">
            <h3>Component Status:</h3>
            <div class="output-item">
                <strong>Dialog Visible:</strong> {{ addUserVisible }}
            </div>
            <div class="output-item">
                <strong>Test Mode:</strong> {{ testMode }}
            </div>
            <div class="output-item">
                <strong>Form Data:</strong>
                <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
            </div>
        </div>

        <!-- AddUser Component -->
        <el-drawer
            v-model="addUserVisible"
            :size="486"
            :lockScroll="true"
            :show-close="false"
            title="Test Add User"
        >
            <template #header>
                <div class="drawer-header flex items-center justify-between leading-5.5">
                    <div class="drawer-header">
                        <span>{{ testMode === 'add' ? 'Add User' : 'Edit User' }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDialog">Cancel</el-button>
                        <el-button plain round type="primary" @click="testSubmit">Submit</el-button>
                    </div>
                </div>
            </template>
            
            <add-user 
                ref="addUserRef" 
                :data="testMode === 'edit' ? editData : null" 
                :sites="mockSites" 
            />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AddUser from '@/views/role/components/addUser.vue'

const addUserVisible = ref(false)
const testMode = ref('add')
const addUserRef = ref(null)

const formData = ref({})

const editData = ref({
    staffId: '123',
    realName: 'Test User',
    phone: '13800138000',
    email: '<EMAIL>',
    permissions: [],
    resourceScope: 'all',
    viewableStationIds: []
})

const mockSites = ref([
    {
        id: '1',
        name: 'Site 1',
        children: [
            { id: '1-1', name: 'Station 1-1' },
            { id: '1-2', name: 'Station 1-2' }
        ]
    },
    {
        id: '2',
        name: 'Site 2',
        children: [
            { id: '2-1', name: 'Station 2-1' },
            { id: '2-2', name: 'Station 2-2' }
        ]
    }
])

const openAddUser = () => {
    addUserVisible.value = true
}

const closeDialog = () => {
    addUserVisible.value = false
    if (addUserRef.value) {
        addUserRef.value.clearValidate()
    }
}

const setEditData = () => {
    editData.value = {
        staffId: '123',
        realName: 'John Doe',
        phone: '13912345678',
        email: '<EMAIL>',
        permissions: ['emsControl'],
        resourceScope: 'specify',
        viewableStationIds: ['1-1', '2-1']
    }
    ElMessage.success('Edit data set!')
}

const testSubmit = async () => {
    if (addUserRef.value) {
        try {
            const isValid = await addUserRef.value.submitRules()
            if (isValid) {
                formData.value = { ...addUserRef.value.formState }
                ElMessage.success('Form validation passed!')
                console.log('Form data:', formData.value)
            } else {
                ElMessage.error('Form validation failed!')
            }
        } catch (error) {
            ElMessage.error('Validation error: ' + error.message)
        }
    }
}
</script>

<style lang="less" scoped>
.add-user-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.test-controls {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.output-display {
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f0f9ff;
}

.output-item {
    margin-bottom: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 4px;
    border-left: 4px solid #409eff;
    
    pre {
        margin: 10px 0 0 0;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 4px;
        font-size: 12px;
        overflow-x: auto;
    }
}

h2 {
    color: #303133;
    margin-bottom: 20px;
}

h3 {
    color: #606266;
    margin-bottom: 15px;
}

.drawer-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
