<template>
    <div class="add-sub-account-test">
        <h2>AddSubAccount Component Test</h2>
        
        <div class="test-controls">
            <el-form :model="testForm" label-width="120px">
                <el-form-item label="Test Mode:">
                    <el-radio-group v-model="testMode">
                        <el-radio label="add">Add SubAccount</el-radio>
                        <el-radio label="edit">Edit SubAccount</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="openDialog">Open Dialog</el-button>
                    <el-button @click="changeEditData" style="margin-left: 10px;">Change Edit Data</el-button>
                    <el-button @click="forceUpdate" style="margin-left: 10px;">Force Update</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="output-display">
            <h3>Component Status:</h3>
            <div class="output-item">
                <strong>Dialog Visible:</strong> {{ dialogVisible }}
            </div>
            <div class="output-item">
                <strong>Test Mode:</strong> {{ testMode }}
            </div>
            <div class="output-item">
                <strong>Selected Org:</strong>
                <pre>{{ JSON.stringify(selectedOrg, null, 2) }}</pre>
            </div>
        </div>

        <!-- Dialog -->
        <el-drawer
            v-model="dialogVisible"
            :size="486"
            :lockScroll="true"
            :show-close="false"
            title="Test AddSubAccount"
        >
            <template #header>
                <div class="drawer-header flex items-center justify-between leading-5.5">
                    <div class="drawer-header">
                        <span>{{ testMode === 'add' ? 'Add SubAccount' : 'Edit SubAccount' }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDialog">Cancel</el-button>
                        <el-button plain round type="primary" @click="testSubmit">Submit</el-button>
                    </div>
                </div>
            </template>
            
            <add-sub-account 
                ref="addSubAccountRef" 
                :selectedOrg="selectedOrg"
                :openType="testMode"
            />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AddSubAccount from '@/views/role/components/addSubAccount.vue'

const dialogVisible = ref(false)
const testMode = ref('add')
const addSubAccountRef = ref(null)

const selectedOrg = ref({})

// 模拟不同的编辑数据
const editDataList = [
    {
        id: '1',
        name: 'Test Company 1',
        adminName: 'John Doe',
        adminPhone: '***********',
        adminEmail: '<EMAIL>',
        parentId: 'parent1',
        parentName: 'Parent Company 1',
        orgType: 'supplier',
        businessType: 'energy_storage_cabinet',
        openType: 'edit'
    },
    {
        id: '2',
        name: 'Test Company 2',
        adminName: 'Jane Smith',
        adminPhone: '***********',
        adminEmail: '<EMAIL>',
        parentId: 'parent2',
        parentName: 'Parent Company 2',
        orgType: 'customer',
        businessType: 'vehicle_battery',
        openType: 'edit'
    },
    {
        id: '3',
        name: 'Test Company 3',
        adminName: 'Bob Johnson',
        adminPhone: '***********',
        adminEmail: '<EMAIL>',
        parentId: 'parent3',
        parentName: 'Parent Company 3',
        orgType: 'supplier',
        businessType: 'all',
        openType: 'edit'
    }
]

let currentEditIndex = 0

const openDialog = () => {
    if (testMode.value === 'add') {
        selectedOrg.value = {
            id: 'parent1',
            name: 'Parent Company',
            openType: 'add'
        }
    } else {
        selectedOrg.value = { ...editDataList[currentEditIndex] }
    }
    dialogVisible.value = true
}

const closeDialog = () => {
    dialogVisible.value = false
    if (addSubAccountRef.value) {
        addSubAccountRef.value.clearValidate()
    }
}

const changeEditData = () => {
    if (testMode.value === 'edit') {
        currentEditIndex = (currentEditIndex + 1) % editDataList.length
        selectedOrg.value = { ...editDataList[currentEditIndex] }
        ElMessage.success(`切换到编辑数据 ${currentEditIndex + 1}`)
    } else {
        ElMessage.warning('请先切换到编辑模式')
    }
}

const forceUpdate = () => {
    if (addSubAccountRef.value && testMode.value === 'edit') {
        addSubAccountRef.value.forceUpdateEditData(selectedOrg.value)
        ElMessage.success('强制更新数据完成')
    } else {
        ElMessage.warning('请先打开编辑模式的对话框')
    }
}

const testSubmit = async () => {
    if (addSubAccountRef.value) {
        try {
            const result = await addSubAccountRef.value.submitRules()
            ElMessage.success('表单验证通过!')
            console.log('Form data:', result)
        } catch (error) {
            ElMessage.error('表单验证失败!')
        }
    }
}
</script>

<style lang="less" scoped>
.add-sub-account-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.test-controls {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.output-display {
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f0f9ff;
}

.output-item {
    margin-bottom: 15px;
    padding: 10px;
    background-color: white;
    border-radius: 4px;
    border-left: 4px solid #409eff;
    
    pre {
        margin: 10px 0 0 0;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 4px;
        font-size: 12px;
        overflow-x: auto;
    }
}

h2 {
    color: #303133;
    margin-bottom: 20px;
}

h3 {
    color: #606266;
    margin-bottom: 15px;
}

.drawer-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
