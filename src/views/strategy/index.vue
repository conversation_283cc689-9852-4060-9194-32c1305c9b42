<template>
    <div class="strategy">
        <div class="w-full fixed left-0 top-0 z-50">
            <mw-header class="relative" style="z-index: 100" />
        </div>
        <div>
            <div class="content-area">
                <!-- <div class="flex mb-4 justify-between">
                    <div class="my-text-base go-box inline-block cursor-pointer" @click="goBackRouter">
                        <span class="bt-box-go rounded"><i class="iconfont icon-jichu-you"></i></span><span
                            class="ml-2">{{ stationName }}</span>
                    </div>
                </div> -->
                <div class="flex mb-4 justify-between">
                    <div
                        class="text-base leading-8 go-box flex items-center text-primary-text dark:text-80-dark"
                    >
                        <span
                            class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                            @click="goBackRouter"
                            >{{ stationName || '-' }}</span
                        >
                        <iconSvg
                            name="rightIcon"
                            class="more-icon text-primary-text dark:text-80-dark"
                        />
                        <!-- 站点详情 -->
                        <span
                            class="ml-1 text-primary-text dark:text-80-dark"
                            >{{ '策略管理' }}</span
                        >
                    </div>
                </div>
                <div
                    class="bg-ff dark:bg-ff-dark rounded pb-4 strategy-content"
                >
                    <div
                        class="flex mb-2 p-4 pb-2 items-center"
                        v-if="showMenu"
                    >
                        <div class="">
                            <div class="flex items-center menu-tabs gap-x-8">
                                <div
                                    class="menu-tab cursor-pointer font-medium"
                                    :class="
                                        currentMenu == item.name ? 'active' : ''
                                    "
                                    v-for="item in menus"
                                    :key="item.name"
                                    @click="gotoRouter(item)"
                                >
                                    {{ item.title }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <router-view />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import mwHeader from '@/components/homeHeader.vue'
import { ref, computed } from 'vue'
const router = useRouter(),
    route = useRoute()
const menus = ref([
    {
        name: 'energy',
        title: '能效管理',
    },
    {
        name: 'price',
        title: '电价管理',
    },
    {
        name: 'ring',
        title: '动环管理',
    },
])
const currentMenu = computed(() => {
    return route.name
})
const showMenu = computed(() => {
    return route.meta.showMenu
})
const goBackRouter = () => {
    router.go(-1)
    // router.push({
    //     name: 'deviceDetail',
    //     // query: { stationNo, stationOrgId: selectedKeys.value[0] },
    //     // params: { stationPic: record.stationPic },
    // })
}
const stationName = computed(() => {
    return route.query.stationName
})
const gotoRouter = (item) => {
    //
    router.replace({
        name: item.name,
        query: {
            stationNo: route.query.stationNo,
            stationId: route.query.stationId,
            stationOrgId: route.query.stationOrgId,
            stationName: route.query.stationName,
        },
    })
}
</script>

<style lang="less" scoped>
.go-box {
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: rgba(34, 34, 34, 0.6);

    .bt-box-go {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        background-color: #fff;
        text-align: center;

        &:hover {
            background-color: var(--themeColor);
            color: #fff;
        }
    }
}

.menu-tab {
    position: relative;
    line-height: 24px;
    padding-bottom: 8px;
    color: var(--text-100);
    &::after {
        display: block;
        content: '';
        width: 40px;
        height: 4px;
        background: transparent;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
    }

    &.active {
        color: var(--themeColor);

        &::after {
            background: var(--themeColor);
        }
    }
}

:deep(.ant-btn-primary) {
    background: var(--themeColor);
    border-color: var(--themeColor);
    outline-color: var(--themeColor);
}

:deep(.ant-btn-primary) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);

    &:hover {
        color: #fff;
        border-color: var(--themeColor);
    }
}

:deep(
        .ant-btn-primary[disabled],
        .ant-btn-primary[disabled]:hover,
        .ant-btn-primary[disabled]:focus,
        .ant-btn-primary[disabled]:active
    ) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);
    opacity: 0.6;
}

.strategy-content {
    min-height: calc(~'100vh - 170px');
}

:deep(.ant-btn) {
    border-color: transparent;
    background: #f5f7f7;
    color: rgba(33, 33, 33, 0.6);
    border-color: transparent;
}

:deep(.ant-btn.ant-btn-primary) {
    background: var(--themeColor);
    color: #fff;
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
}
.strategy {
    padding-top: 88px;
    min-height: 100vh;
    background-color: var(--main-bg);
}
</style>
