<template>
    <div class="qr-example-container">
        <h2>二维码跳转功能演示</h2>

        <!-- 功能说明 -->
        <div class="description">
            <h3>功能说明</h3>
            <ul>
                <li>移动端扫码：跳转到H5页面 <code>/h5?sn=设备编号</code></li>
                <li>
                    微信扫码：跳转到微信小程序
                    <code>packageWeb/index?sn=设备编号</code>
                </li>
                <li>自动检测环境并选择最佳跳转方式</li>
            </ul>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h3>测试功能</h3>

            <div class="input-group">
                <label>设备编号 (SN):</label>
                <input
                    v-model="testSn"
                    type="text"
                    placeholder="请输入设备编号"
                    class="sn-input"
                />
            </div>

            <div class="button-group">
                <button @click="testSmartRedirect" class="btn primary">
                    智能跳转测试
                </button>
                <button @click="testH5Redirect" class="btn secondary">
                    强制H5跳转
                </button>
                <button @click="testMiniProgramRedirect" class="btn secondary">
                    强制小程序跳转
                </button>
                <button @click="openQRRedirectPage" class="btn info">
                    打开跳转页面
                </button>
            </div>
        </div>

        <!-- 二维码生成 -->
        <div class="qr-section">
            <h3>二维码生成</h3>
            <div class="qr-generator">
                <div class="qr-input">
                    <label>生成二维码链接:</label>
                    <div class="url-display">
                        {{ qrUrl }}
                    </div>
                    <button @click="copyUrl" class="btn copy">复制链接</button>
                </div>

                <div class="qr-code" v-if="qrUrl">
                    <div class="qr-placeholder">
                        <p>二维码预览</p>
                        <p class="qr-text">{{ qrUrl }}</p>
                        <small>扫描此二维码将跳转到对应页面</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 环境检测信息 -->
        <div class="env-info">
            <h3>当前环境信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">是否微信环境:</span>
                    <span class="value" :class="{ active: envInfo.isWechat }">
                        {{ envInfo.isWechat ? '是' : '否' }}
                    </span>
                </div>
                <div class="info-item">
                    <span class="label">是否移动端:</span>
                    <span class="value" :class="{ active: envInfo.isMobile }">
                        {{ envInfo.isMobile ? '是' : '否' }}
                    </span>
                </div>
                <div class="info-item">
                    <span class="label">是否小程序:</span>
                    <span
                        class="value"
                        :class="{ active: envInfo.isMiniProgram }"
                    >
                        {{ envInfo.isMiniProgram ? '是' : '否' }}
                    </span>
                </div>
                <div class="info-item full-width">
                    <span class="label">用户代理:</span>
                    <span class="value user-agent">{{
                        envInfo.userAgent
                    }}</span>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="usage-guide">
            <h3>使用说明</h3>
            <div class="code-example">
                <h4>1. 直接调用智能跳转</h4>
                <pre><code>import { smartRedirect } from '@/utils/qrCodeRedirect'

// 智能跳转
await smartRedirect('设备编号', {
    miniProgramAppId: 'your-miniprogram-appid',
    h5BaseUrl: 'https://your-domain.com'
})</code></pre>

                <h4>2. 生成跳转页面链接</h4>
                <pre><code>// 生成二维码链接
const qrUrl = `${window.location.origin}/#/qr-redirect?sn=设备编号`</code></pre>

                <h4>3. 在其他组件中使用</h4>
                <pre><code>// 在bar组件中已经集成了goAlarm方法
const barRef = ref()
barRef.value?.goAlarm('设备编号')</code></pre>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
    smartRedirect,
    redirectToH5,
    redirectToMiniProgram,
    isWechat,
    isMobile,
    isMiniProgram,
} from '@/utils/qrCodeRedirect'

const router = useRouter()

// 响应式数据
const testSn = ref('866597074507136')

// 环境信息
const envInfo = ref({
    isWechat: false,
    isMobile: false,
    isMiniProgram: false,
    userAgent: '',
})

// 配置选项
const redirectOptions = {
    miniProgramAppId: 'your-miniprogram-appid', // 替换为实际的小程序AppId
    h5BaseUrl: window.location.origin,
}

// 生成二维码URL
const qrUrl = computed(() => {
    if (!testSn.value) return ''
    return `${window.location.origin}/#/qr-redirect?sn=${testSn.value}`
})

// 检测环境信息
const detectEnvironment = async () => {
    envInfo.value = {
        isWechat: isWechat(),
        isMobile: isMobile(),
        isMiniProgram: await isMiniProgram(),
        userAgent: navigator.userAgent,
    }
}

// 智能跳转测试
const testSmartRedirect = async () => {
    if (!testSn.value) {
        alert('请输入设备编号')
        return
    }

    try {
        await smartRedirect(testSn.value, redirectOptions)
    } catch (error) {
        console.error('智能跳转失败:', error)
        alert('跳转失败: ' + error.message)
    }
}

// H5跳转测试
const testH5Redirect = () => {
    if (!testSn.value) {
        alert('请输入设备编号')
        return
    }

    redirectToH5(testSn.value, redirectOptions.h5BaseUrl)
}

// 小程序跳转测试
const testMiniProgramRedirect = () => {
    if (!testSn.value) {
        alert('请输入设备编号')
        return
    }

    redirectToMiniProgram(testSn.value, redirectOptions.miniProgramAppId)
}

// 打开跳转页面
const openQRRedirectPage = () => {
    if (!testSn.value) {
        alert('请输入设备编号')
        return
    }

    router.push({
        path: '/qr-redirect',
        query: { sn: testSn.value },
    })
}

// 复制链接
const copyUrl = async () => {
    try {
        await navigator.clipboard.writeText(qrUrl.value)
        alert('链接已复制到剪贴板')
    } catch (error) {
        console.error('复制失败:', error)
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = qrUrl.value
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        alert('链接已复制到剪贴板')
    }
}

onMounted(() => {
    detectEnvironment()
})
</script>

<style lang="less" scoped>
.qr-example-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        sans-serif;

    h2,
    h3 {
        color: #333;
        margin-bottom: 20px;
    }

    h2 {
        text-align: center;
        font-size: 28px;
        margin-bottom: 30px;
    }

    h3 {
        font-size: 20px;
        border-bottom: 2px solid #3edacd;
        padding-bottom: 8px;
    }
}

.description {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;

    ul {
        margin: 10px 0;
        padding-left: 20px;

        li {
            margin-bottom: 8px;
            line-height: 1.5;

            code {
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
            }
        }
    }
}

.test-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .input-group {
        margin-bottom: 20px;

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .sn-input {
            width: 100%;
            max-width: 400px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;

            &:focus {
                outline: none;
                border-color: #3edacd;
            }
        }
    }

    .button-group {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;

    &.primary {
        background: #3edacd;
        color: white;

        &:hover {
            background: #35c5b8;
        }
    }

    &.secondary {
        background: #6c757d;
        color: white;

        &:hover {
            background: #5a6268;
        }
    }

    &.info {
        background: #17a2b8;
        color: white;

        &:hover {
            background: #138496;
        }
    }

    &.copy {
        background: #28a745;
        color: white;

        &:hover {
            background: #218838;
        }
    }
}

.qr-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .qr-generator {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 20px;

        .qr-input {
            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .url-display {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
                word-break: break-all;
                font-family: 'Courier New', monospace;
                font-size: 14px;
            }
        }

        .qr-code {
            .qr-placeholder {
                border: 2px dashed #ddd;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                background: #f9f9f9;

                .qr-text {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    word-break: break-all;
                    margin: 10px 0;
                    color: #666;
                }

                small {
                    color: #999;
                }
            }
        }
    }
}

.env-info {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;

    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;

            &.full-width {
                grid-column: 1 / -1;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .label {
                font-weight: 500;
                color: #555;
            }

            .value {
                color: #666;

                &.active {
                    color: #28a745;
                    font-weight: 600;
                }

                &.user-agent {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    word-break: break-all;
                }
            }
        }
    }
}

.usage-guide {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;

    .code-example {
        h4 {
            color: #495057;
            margin: 20px 0 10px;
            font-size: 16px;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            margin-bottom: 20px;

            code {
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.5;
            }
        }
    }
}

@media (max-width: 768px) {
    .qr-generator {
        grid-template-columns: 1fr !important;
    }

    .info-grid {
        grid-template-columns: 1fr !important;
    }

    .button-group {
        flex-direction: column;
    }
}
</style>
