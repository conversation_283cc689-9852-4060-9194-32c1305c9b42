<template>
    <div class="relative pt-4">
        <div class="top flex justify-end absolute -top-6 right-0">
            <div></div>
            <div class="flex items-center">
                <el-select
                    v-model="operationLogModule"
                    placeholder="事件类型"
                    style="width: 120px"
                    class="ml-4 mr-4"
                    filterable
                    clearable
                    @change="onChange"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                <date-search
                    :info="{
                        periodOptions: [],
                        datePickerType: 'day',
                        defaultDayRangeLen: 30,
                        dayRangeLen: 7,
                        setNull: true,
                    }"
                    v-model:dateSelect="dateSelect"
                    @onChange="dateChange"
                />
                <div class="">
                    <!-- <el-button round class="btn-hover" @click="onSearch">
                        <span class="icon-box">
                            <iconSvg name="search" class="icon-default" />
                        </span>
                        <span>查询</span>
                    </el-button> -->
                    <el-popconfirm
                        :title="
                            '是否确认导出' +
                            (dateSelect?.startDate || dateSelect?.startMonth) +
                            '至' +
                            (dateSelect?.endDate || dateSelect?.endMonth) +
                            '的操作日志数据?'
                        "
                        @confirm="exportExcel"
                        confirm-button-text="是"
                        cancel-button-text="否"
                        width="240"
                    >
                        <template #reference>
                            <export-button :disabled="!dataSource.length" />
                        </template>
                    </el-popconfirm>
                </div>
            </div>
        </div>
        <!--  -->
        <mw-table
            :dataSource="dataSource"
            :columns="columns"
            :hasPage="true"
            :loading="loading"
            :rowKey="(record) => record.id"
            :pageConfig="{ changePage, paginationProps }"
            @change="onTableChange"
            :showRefresh="false"
            class="alarm-table"
        >
            <template #type="{ record }">
                {{ getOperationName(record.operationLogModule) }} -
                {{ record.operationLogTypeName }}
            </template>
            <template #operator="{ record }">
                {{ record.staffName }}
            </template>
            <template #createTime="{ record }">
                {{ record.createTime }}
            </template>
        </mw-table>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { usePagenation } from '@/common/setup'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService'
import DateSearch from '@/components/dateSearch.vue'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
import moment from 'moment'
const dateSelect = ref({
    periodType: 'day',
    startDate: moment().subtract(7, 'days').format('YYYY-MM-DD'),
    endDate: moment().format('YYYY-MM-DD'),
})
const columns = [
    {
        title: '操作内容',
        dataIndex: 'content',
        key: 'content',
        slots: {
            customRender: 'content',
        },
        width: '40%',
    },
    {
        title: '事件类型',
        dataIndex: 'type',
        key: 'type',
        slots: {
            customRender: 'type',
        },

        align: 'left',
    },
    {
        title: '操作人',
        dataIndex: 'operator',
        key: 'operator',
        slots: {
            customRender: 'operator',
        },

        align: 'center',
    },
    {
        title: '发生时间',
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },

        align: 'right',
    },
]
const dataSource = ref([])
const loading = ref(false)
const getData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    const res = await apiVpp.getOperationLogPage({
        ...params,
        ...dateSelect.value,
        operationLogModule: operationLogModule.value,
    })
    paginationProps.value.total = res.data.data.total
    paginationProps.value.current = res.data.data.current
    dataSource.value = res.data.data.records
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)
const exportExcel = () => {
    // 导出excel
    let data = dataSource.value.map((obj) => ({
        操作内容: obj?.content || '',
        事件类型: obj?.operationLogTypeName || 0,
        操作人: obj?.staffName || 0,
        发生时间: obj?.createTime || 0,
    }))
    // 将数据转换为 worksheet 对象
    const worksheet = XLSX.utils.json_to_sheet(data)
    // 将 worksheet 对象添加到 workbook 中
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    // Excel 文件
    XLSX.writeFile(workbook, '操作日志.xlsx')
}
const onChange = async () => {
    //
    paginationProps.value.current = 1
    await getData()
}
const dateChange = async () => {
    paginationProps.value.current = 1
    await getData()
}
// const onSearch = async () => {
//     // pageParam.value.current = 1
//     await getData()
// }
const operationLogModule = ref('')
const options = ref([])
const getTypeList = async () => {
    const res = await api.getDictByType({
        type: 'operationLogModule',
    })
    options.value = res.data.data
}
const getOperationName = (val) => {
    return options.value.find((item) => item.value == val)?.label
}
onMounted(async () => {
    await getTypeList()
    await getData()
})
</script>

<style lang="less" scoped>
.ant-table-tbody > tr > td {
    padding: 16px;
    font-size: 14px;
    color: var(--text-80);
}
</style>
