<template>
    <div class="sub-account">
        <mw-table
            :dataSource="dataSource"
            :columns="columns"
            :hasPage="true"
            :pageConfig="{ changePage, paginationProps }"
            :loading="loading"
            :rowKey="(record) => record.id"
            @change="onTableChange"
            :showRefresh="false"
            class="alarm-table"
        >
            <template #businessType="{ record }">
                {{ getBusinessType(record.businessType) }}
            </template>
            <template #orgType="{ record }">
                {{
                    record.orgType == 'supplier'
                        ? $t('common_shi')
                        : $t('common_fou')
                }}
            </template>
        </mw-table>

        <div class="" v-for="item in dataSource" :key="item.id"></div>
        <el-drawer
            v-model="visible"
            :size="486"
            :show-close="false"
            @close="onClose"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{
                            edit
                                ? $t('Edit Sub-account')
                                : $t('Add Sub-account')
                        }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="onClose">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            :loading="addLoading"
                            @click="submit"
                            >{{ $t('Save') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <subAccount ref="subAccountBox" />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import apiService from '@/apiService/device'
import subAccount from './addSubAccount.vue'
import { message } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const dataSource = ref([])

const loading = ref(false)

const visible = ref(false)

const edit = ref(false)

const addLoading = ref(false)

const subAccountBox = ref(null)

const columns = [
    {
        title: t('sub_zichanghumingcheng'),
        dataIndex: 'orgName',
        key: 'orgName',
        slots: {
            customRender: 'orgName',
        },
        width: '24%',
    },
    {
        title: t('sub_guanliyuanxingming'),
        dataIndex: 'realName',
        key: 'realName',
        slots: {
            customRender: 'realName',
        },
        width: '14%',
        align: 'center',
    },
    {
        title: t('phone'),
        dataIndex: 'phone',
        key: 'phone',
        slots: {
            customRender: 'phone',
        },
        width: '16%',
        align: 'center',
    },
    {
        title: t('sub_yewufanwei'),
        dataIndex: 'businessType',
        key: 'businessType',
        slots: {
            customRender: 'businessType',
        },
        width: '16%',
        align: 'center',
    },
    {
        title: t('sub_sfjyktzzhqx'),
        dataIndex: 'orgType',
        key: 'orgType',
        slots: {
            customRender: 'orgType',
        },
        width: '16%',
        align: 'center',
    },
    {
        title: t('sub_tianjiashijian'),
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },
        width: '18%',
        align: 'right',
    },
]
const businesses = [
    {
        value: 'all',
        label: t('All'),
    },
    {
        value: 'energy_storage_cabinet',
        label: t('sub_options_gongshangyechuneng'),
    },
    {
        value: 'vehicle_battery',
        label: t('sub_options_donglidianchi'),
    },
]

const getBusinessType = (type) => {
    if (!type) return '-'
    let res = businesses.find((item) => item.value == type)
    return res ? res.label : '-'
}
const getStaffPageData = async () => {
    const params = {
        // current: pageParam.value.current,
        // size: pageParam.value.size,
        supplierId: '',
        // businessType:businessType
    }
    try {
        loading.value = true
        const res = await apiService.getSubSupplierWithOwnerTree(params)
        loading.value = false
        dataSource.value = res.data.data || []
    } catch (error) {
        loading.value = false
    }
}

const onClose = () => {
    subAccountBox.value.resetFields()
    subAccountBox.value.clearValidate()
    visible.value = false
}

const createSubOrgWithOwner = async (params) => {
    console.log('[ params ] >', params)
    try {
        addLoading.value = true
        params.orgType = params.orgType ? 'supplier' : 'customer'
        const {
            data: { code, msg },
        } = await apiService.createSubOrgWithOwner(params)
        if (code === 0) {
            message.success(t('caozuochenggong'))
            onClose()
        } else {
            // message.error(msg)
        }

        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}

const submit = async () => {
    try {
        // 调用子组件的方法并获取数据
        const formState = await subAccountBox.value.submitRules()
        if (!edit.value) {
            createSubOrgWithOwner(formState)
        }
    } catch (error) {
        console.log('error')
        // 这里处理验证失败的逻辑
    }
}

watch(
    () => visible,
    (val) => {
        if (val) {
            edit.value = false
        }
    }
)
onMounted(() => {
    getStaffPageData()
})

defineExpose({ visible })
</script>

<style lang="less" scoped>
.sub-account {
    .alarm-table {
        :deep(.ant-table-row) {
            cursor: pointer;
        }

        :deep(.ant-table-thead > tr > th) {
            background: none;
            font-weight: none;
            color: var(--text-60) !important;
        }

        :deep(.ant-table-tbody > tr > td) {
            padding: 16px;
            font-size: 14px;
            color: var(--text-80);
        }
    }
}
</style>
