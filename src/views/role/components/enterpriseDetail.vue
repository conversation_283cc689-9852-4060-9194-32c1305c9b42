<template>
    <div class="enterpriseDetail">
        <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            class="add-form"
        >
            <a-form-item label="企业名称" name="orgName">
                <a-input
                    v-model:value="formState.orgName"
                    placeholder="请输入企业名称"
                />
            </a-form-item>
            <a-form-item label="主管理员姓名" name="realName">
                <a-input
                    v-model:value="formState.realName"
                    placeholder="请输入主管理员姓名"
                />
            </a-form-item>
            <a-form-item label="手机号" name="phone">
                <a-input
                    v-model:value="formState.phone"
                    placeholder="请输入手机号"
                    :maxLength="11"
                    disabled
                />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { reactive, watch, ref } from 'vue'
const props = defineProps({ data: { type: Object } })
const formState = reactive({
    realName: void 0,
    phone: void 0,
    orgName: void 0,
})

const formRef = ref(null)

const validator = async (rule, value) => {
    if (!value) {
        return Promise.reject('请输入手机号')
    } else {
        const phoneReg = /^1[3456789]\d{9}$/
        if (phoneReg.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject('手机号不符合规则')
        }
    }
}

const rules = {
    orgName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
    realName: [
        { required: true, message: '请输入主管理员姓名', trigger: 'blur' },
    ],
    phone: [{ required: true, trigger: 'change', validator }],
}

const submitRules = () => {
    return formRef.value.validate()
}

const clearValidate = () => {
    formRef.value.clearValidate()
}

const resetFields = () => {
    formRef.value.resetFields()
}

defineExpose({ submitRules, clearValidate, resetFields })

watch(
    () => props.data,
    (val) => {
        if (val) {
            Object.keys(val).forEach((key) => {
                formState[key] = val[key]
            })
        }
    },
    { immediate: true }
)
</script>

<style scoped lang="less">
.enterpriseDetail {
    :deep(.ant-form) {
        padding-right: 16px;
        padding-left: 5px;

        .ant-form-item-has-success {
            margin-bottom: 24px !important;
        }
        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }
        .ant-form-item {
            font-size: 14px;
            margin-bottom: 24px;
            .ant-form-item-label {
                width: 120px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: rgba(34, 34, 34, 0.65);
                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
            }

            .ant-form-item-control-input {
                min-height: 32px;
                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 11px;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
</style>
