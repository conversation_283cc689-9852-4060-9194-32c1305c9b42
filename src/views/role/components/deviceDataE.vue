<template>
    <div style="width: 100%; height: 340px">
        <div
            v-loading="loading"
            class="tables"
            style="width: 100%; height: 340px"
        >
            <div class="w-full h-full">
                <el-auto-resizer>
                    <template #default="{ height, width }">
                        <el-table-v2
                            :columns="columns"
                            :data="tableData"
                            :width="width"
                            :height="height"
                            fixed
                        >
                            <template #empty>
                                <empty-data
                                    :description="$t('zanwushuju')"
                                    style="margin-top: 100px"
                                >
                                    <slot name="empty"></slot>
                                </empty-data>
                            </template>
                        </el-table-v2>
                    </template>
                </el-auto-resizer>
            </div>
        </div>
    </div>
</template>

<script setup>
import moment from 'moment'
import { watch, nextTick, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    tableColumn: {
        type: Array,
        default: () => [],
    },
    loading: {
        type: Boolean,
        default: false,
    },
    showMore: {
        type: Boolean,
        default: false,
    },
    loadMoreLoading: {
        type: Boolean,
        default: false,
    },
})
const columns = computed(() => {
    console.log(props.tableColumn)

    return props.tableColumn
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}

watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
:deep(.el-table__body-wrapper tr td.el-table-fixed-column--left) {
    background: var(--input-bg);
}
</style>
