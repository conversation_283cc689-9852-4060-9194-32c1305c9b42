<template>
    <div class="add-custom-theme">
        <div class="title">基础配置</div>
        <div class="color-box">
            <div class="color-box-top">
                <span>
                    <iconSvg name="colorIcon" className="color-icon" />
                </span>
                <span class="color-title">系统主题色配置</span>
            </div>
            <div class="relative relative-box">
                <el-button
                    plain
                    type="primary"
                    round
                    :loading="addLoading"
                    @click="isShow = !isShow"
                    >切换颜色</el-button
                >
                <div class="inline-block inline-box">
                    <span>当前色值</span>
                    <a-input v-model:value="pureColor" />
                </div>
                <div v-show="isShow" class="absolute absolute-box">
                    <Vue3ColorPicker
                        v-model:pureColor="pureColor"
                        pickerType="chrome"
                        :isWidget="isShow"
                        format="hex"
                        :zIndex="1"
                        :disableHistory="true"
                        :roundHistory="true"
                        :disableAlpha="true"
                    />
                    <div class="picker-bt">
                        <el-button plain round @click="isShow = false">{{
                            $t('common_guanbi')
                        }}</el-button>
                    </div>
                </div>
            </div>
            <p class="p-title">
                （当前颜色输入色值可进行切换，用于系统主题色配置）
            </p>
            <div class="show-bt-box">
                <span>示例</span>
                <div
                    class="show-bt"
                    :style="{
                        color: '#fff',
                        borderColor: pureColor,
                        backgroundColor: pureColor,
                    }"
                >
                    暗流
                </div>
                <div
                    class="show-bt"
                    :style="{ color: pureColor, borderColor: pureColor }"
                >
                    暗流
                </div>
                <div
                    class="show-bt"
                    :style="{ color: pureColor, borderColor: '#D9D9D9' }"
                >
                    暗流
                </div>
            </div>
            <div>
                <!-- <UploadVue /> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
// import UploadVue from './Upload.vue';
const pureColor = ref('#6FBECE')
const isShow = ref(false)
</script>

<style lang="less" scoped>
.add-custom-theme {
    padding: 0 16px;

    .title {
        height: 22px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: rgba(34, 34, 34, 0.45);
        line-height: 22px;
        margin-bottom: 9px;
    }

    .color-box {
        padding: 16px;
        background: #f5f7f7;
        border-radius: 8px;

        .color-box-top {
            display: flex;
            align-items: center;

            :deep(.color-icon) {
                width: 16px;
                height: 16px;
            }

            .color-title {
                height: 22px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #141414;
                line-height: 22px;
            }
        }

        .inline-box {
            margin-left: 12px;

            span {
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #999999;
                line-height: 20px;
                margin-right: 4px;
            }

            :deep(.ant-input) {
                width: 160px;
                padding: 4px 11px;
                height: 32px;
                font-size: 14px;
            }
        }

        .picker-bt {
            padding: 16px;
            text-align: right;
            box-shadow: 0 0 10px #00000026;
        }

        .relative-box {
            padding-left: 20px;
            display: flex;
            align-items: center;
            margin-top: 7px;

            .absolute-box {
                top: 34px;
                left: 20px;
            }
        }

        .p-title {
            padding-left: 20px;
            height: 22px;
            font-size: 12px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            color: rgba(34, 34, 34, 0.45);
            line-height: 22px;
            margin-top: 4px;
        }

        .show-bt-box {
            padding-left: 20px;
            margin-top: 12px;

            span {
                height: 20px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #999999;
                line-height: 20px;
            }

            .show-bt {
                height: 24px;
                line-height: 22px;
                border: 1px solid #fff;
                border-radius: 2px;
                font-size: 14px;
                display: inline-block;
                margin-left: 12px;
                padding: 0 12px;
            }
        }
    }

    .bt {
        padding: 4px 15px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        height: 32px;
    }
}
</style>
