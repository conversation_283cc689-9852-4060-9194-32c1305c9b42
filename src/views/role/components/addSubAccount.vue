<template>
    <div class="add-sub-account relative">
        <el-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            :label-width="labelWidth"
            label-position="left"
        >
            <el-form-item :label="$t('sub_zichanghumingcheng')" prop="orgName">
                <el-input
                    v-model="formState.orgName"
                    :placeholder="$t('placeholder_qingshuruzizhanghumingcheng')"
                />
            </el-form-item>
            <el-form-item :label="$t('shangjiqiye')">
                <el-input
                    disabled
                    v-model="currentOrgName"
                    :placeholder="$t('')"
                />
            </el-form-item>
            <el-form-item :label="$t('sub_yewufanwei')" prop="businessType">
                <el-select
                    :disabled="selectedOrg.openType == 'edit'"
                    :class="selectedOrg.openType == 'edit' ? 'disbaled' : ''"
                    v-model="formState.businessType"
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="3"
                    :placeholder="$t('placeholder_qingxuanzeyewufanwei')"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('sub_guanliyuanxingming')" prop="realName">
                <el-input
                    v-model="formState.realName"
                    :placeholder="$t('placeholder_qingshuruguanliyuanxingming')"
                    :disabled="selectedOrg.openType == 'edit'"
                />
            </el-form-item>
            <el-form-item :label="$t('sub_guanliyuanshouji')" prop="phone">
                <el-input
                    v-model="formState.phone"
                    :placeholder="
                        $t('placeholder_qingshuruguanliyuanshoujihaoma')
                    "
                    :disabled="selectedOrg.openType == 'edit'"
                >
                    <template #prefix>
                        <span
                            class="text-third-text dark:text-60-dark inline-block px-1.5 rounded-full bg-f5f7f7 dark:bg-transparent"
                            style="
                                height: 22px;
                                line-height: 22px;
                                margin-left: 0;
                            "
                            >+86</span
                        >
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item :label="$t('Administrator Email')" prop="email">
                <el-input
                    v-model="formState.email"
                    :placeholder="$t('placeholder_qingshuru')"
                    type="email"
                    :disabled="selectedOrg.openType == 'edit'"
                />
            </el-form-item>
            <el-form-item
                :label="$t('Initial Password')"
                name="password"
                prop="password"
                v-if="selectedOrg.openType == 'add'"
            >
                <el-input
                    v-model="formState.password"
                    :placeholder="$t('placeholder_qingshuru')"
                    :type="passwordVisible ? 'text' : 'password'"
                    :maxLength="20"
                >
                    <template #suffix>
                        <div class="password-actions select-none">
                            <div
                                @click="togglePasswordVisibility"
                                :title="
                                    passwordVisible
                                        ? $t('Hide Password')
                                        : $t('Show Password')
                                "
                                class="cursor-pointer text-base"
                            >
                                <EyeOutlined v-if="passwordVisible" />
                                <EyeInvisibleOutlined v-else />
                            </div>
                            <span
                                @click="copyPassword"
                                :title="$t('Copy')"
                                class="cursor-pointer copy-icon"
                            >
                                <iconSvg name="copy" class="w-4 h-4" />
                            </span>
                        </div>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item
                :label="$t('sub_kaitongzizhanghuquanxian')"
                prop="orgType"
                :label-width="subLabelWidth"
            >
                <el-switch
                    v-model="formState.orgType"
                    :disabled="
                        selectedOrg.openType == 'edit' ||
                        selectedOrg?.orgType == 'customer'
                    "
                />
            </el-form-item>
            <p class="p-title">
                {{ $t('sub_tips01') }}
            </p>
        </el-form>
    </div>
</template>

<script setup>
import { reactive, ref, computed, toRaw, watch, nextTick } from 'vue'
// import { useStore } from 'vuex'
// const store = useStore()
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import { email, newPassword } from '@/common/reg'
import iconSvg from '@/components/svgIcon'
import _cloneDeep from 'lodash/cloneDeep'
const { t, locale } = useI18n()
const props = defineProps({
    selectedOrg: {
        type: Object,
        default: () => ({}),
    },
    openType: {
        type: String,
        default: '',
    },
})
const labelWidth = computed(() => {
    let res =
        locale.value == 'zh'
            ? '100px'
            : locale.value == 'en'
            ? '160px'
            : '160px'
    return res
})
const subLabelWidth = computed(() => {
    let res =
        locale.value == 'zh'
            ? '128px'
            : locale.value == 'en'
            ? '240px'
            : '200px'
    return res
})
const formState = reactive({
    orgName: void 0,
    realName: void 0,
    phone: void 0,
    orgType: false,
    businessType: undefined,
    parentOrgId: undefined,
    orgId: undefined,
    email: void 0,
    password: void 0,
})

// 密码显示状态
const passwordVisible = ref(false)

const clearValidate = () => {
    formRef.value.clearValidate()
}

const resetFields = () => {
    formRef?.value && formRef?.value.resetFields()
    formState.parentOrgId = void 0
    currentOrgName.value = ''
    formState.orgName = void 0
    formState.realName = void 0
    formState.phone = void 0
    formState.orgType = void 0
    formState.businessType = void 0
    formState.orgId = void 0
    formState.email = void 0
    formState.password = void 0
    passwordVisible.value = false
}

// 强制更新编辑数据的方法
const forceUpdateEditData = (data) => {
    if (data && data.openType === 'edit') {
        const obj = _cloneDeep(data)

        formState.parentOrgId = obj.parentId
        currentOrgName.value = obj.parentName ? obj.parentName : obj.name
        formState.orgName = obj.name
        formState.realName = obj.adminName
        formState.phone = obj.adminPhone
        formState.orgType = obj.orgType == 'supplier' ? true : false
        formState.businessType = obj.businessType
        formState.orgId = obj.id
        formState.email = obj.adminEmail

        // 清除表单验证状态
        nextTick(() => {
            if (formRef.value) {
                formRef.value.clearValidate()
            }
        })
    }
}

const currentOrgName = ref('')

// 监听selectedOrg的变化，确保数据实时更新
watch(
    () => props.selectedOrg,
    (val, oldVal) => {
        if (val && val.id) {
            if (val.openType == 'add') {
                resetFields()
                formState.parentOrgId = val.id
                formState.businessType = ''
                currentOrgName.value = val.name
            } else if (val.openType == 'edit') {
                const obj = _cloneDeep(val)

                // 立即更新数据，不使用nextTick
                formState.parentOrgId = obj.parentId
                currentOrgName.value = obj.parentName
                    ? obj.parentName
                    : obj.name
                formState.orgName = obj.name
                formState.realName = obj.adminName
                formState.phone = obj.adminPhone
                formState.orgType = obj.orgType == 'supplier' ? true : false
                formState.businessType = obj.businessType
                formState.orgId = obj.id
                formState.email = obj.adminEmail
                // 清除表单验证状态
                nextTick(() => {
                    if (formRef.value) {
                        formRef.value.clearValidate()
                    }
                })
            }
        } else {
            resetFields()
            formState.parentOrgId = ''
            currentOrgName.value = ''
        }
    },
    { immediate: true, deep: true }
)

// 额外监听selectedOrg的特定属性，确保编辑数据的实时更新
watch(
    () => [
        props.selectedOrg?.id,
        props.selectedOrg?.openType,
        props.selectedOrg?.name,
        props.selectedOrg?.adminName,
    ],
    (
        [newId, newOpenType, newName, newAdminName],
        [oldId, , oldName, oldAdminName]
    ) => {
        // 当关键属性发生变化时，强制更新数据
        if (
            newId &&
            newOpenType === 'edit' &&
            (newId !== oldId ||
                newName !== oldName ||
                newAdminName !== oldAdminName)
        ) {
            forceUpdateEditData(props.selectedOrg)
        }
    },
    { immediate: false }
)

// const isSupplier = computed(()=>{
//     return store.state.user.userInfoData.orgType == 'supplier'
// })

// if(!isSupplier.value){
//     formState.orgType = true
// }

const businesses = [
    {
        value: 'all',
        label: t('All'),
    },
    {
        value: 'energy_storage_cabinet',
        label: t('sub_options_gongshangyechuneng'),
    },
    {
        value: 'vehicle_battery',
        label: t('sub_options_donglidianchi'),
    },
]
const options = computed(() => {
    let type = props.selectedOrg.businessType
    // let type = localStorage.getItem('businessType')
    if (type == 'all') {
        return businesses
    } else {
        return businesses.filter((item) => item.value == type)
    }
})
const formRef = ref(null)

// 手机号或邮箱至少有一个的验证器
const phoneValidator = async (rule, value) => {
    const phone = formState.phone
    const emailValue = formState.email

    // 检查手机号格式
    if (phone) {
        const phoneReg = /^1[3456789]\d{9}$/
        if (!phoneReg.test(phone)) {
            return Promise.reject(t('Please enter a valid phone number'))
        }
    }
    // 检查是否至少有一个
    if (!phone && !emailValue) {
        return Promise.reject(
            t('Please enter at least one phone number or email')
        )
    }

    return Promise.resolve()
}
// 手机号或邮箱至少有一个的验证器
const emailValidator = async (rule, value) => {
    const phone = formState.phone
    const emailValue = formState.email

    // 检查邮箱格式
    if (emailValue) {
        if (!email.test(emailValue)) {
            return Promise.reject(t('Please enter a valid email'))
        }
    }

    // 检查是否至少有一个
    if (!phone && !emailValue) {
        return Promise.reject(
            t('Please enter at least one phone number or email')
        )
    }
    return Promise.resolve()
}

// 密码验证器
const passwordValidator = async (rule, value) => {
    if (!value) {
        return Promise.reject(t('placeholder_qingshuru'))
    } else {
        if (newPassword.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(
                t(
                    'Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)'
                )
            )
        }
    }
}

const rules = computed(() => {
    const baseRules = {
        orgName: [
            {
                required: true,
                message: t('placeholder_qingshuruzizhanghumingcheng'),
                trigger: 'blur',
            },
        ],
    }

    // 只有在添加模式下才验证其他字段
    if (props.selectedOrg.openType !== 'edit') {
        return {
            ...baseRules,
            realName: [
                {
                    required: true,
                    message: t('placeholder_qingshuruguanliyuanxingming'),
                    trigger: 'blur',
                },
            ],
            businessType: [
                {
                    required: true,
                    message: t('placeholder_qingxuanzeyewufanwei'),
                    trigger: 'change',
                },
            ],
            phone: [
                {
                    required: false,
                    trigger: ['change', 'blur'],
                    validator: phoneValidator,
                },
            ],
            email: [
                {
                    required: false,
                    trigger: ['change', 'blur'],
                    validator: emailValidator,
                },
            ],
            password: [
                {
                    required: true,
                    trigger: ['change', 'blur'],
                    validator: passwordValidator,
                },
            ],
            orgType: [
                {
                    required: false,
                    message: t('placeholder_qingxuanze'),
                    trigger: 'change',
                    type: 'boolean',
                },
            ],
        }
    }

    return baseRules
})

const submitRules = () => {
    return new Promise((resolve, reject) => {
        // 编辑模式下只验证orgName字段
        console.log(props.selectedOrg.openType, 'props.selectedOrg.openType')
        console.log('formState.orgName:', formState.orgName)

        if (props.selectedOrg.openType === 'edit') {
            console.log('编辑模式验证开始')
            console.log('当前formState:', toRaw(formState))

            // 检查orgName是否有值
            if (!formState.orgName || formState.orgName.trim() === '') {
                console.log('orgName为空，验证失败')
                message.error(t('placeholder_qingshuruzizhanghumingcheng'))
                reject(false)
                return
            }

            // 直接返回成功，因为orgName有值就足够了
            console.log('编辑模式：orgName有值，直接返回成功')
            resolve({ ...toRaw(formState) })

            // 如果需要使用表单验证，可以使用下面的代码
            /*
            formRef.value.validateField('orgName', (errorMessage) => {
                console.log('validateField回调，errorMessage:', errorMessage)
                console.log('errorMessage类型:', typeof errorMessage)

                // 在Ant Design Vue中，验证成功时errorMessage为undefined或null
                // 验证失败时errorMessage为错误信息字符串
                if (errorMessage === undefined || errorMessage === null || errorMessage === '') {
                    console.log('验证成功，返回formState')
                    resolve({ ...toRaw(formState) })
                } else {
                    console.log('验证失败，错误信息:', errorMessage)
                    reject(false)
                }
            })
            */
        } else {
            // 添加模式下验证所有字段
            formRef.value.validate((valid) => {
                if (valid) {
                    // 检查手机号或邮箱至少有一个
                    const phone = formState.phone
                    const emailValue = formState.email

                    if (!phone && !emailValue) {
                        message.error(
                            t('Please enter at least one phone number or email')
                        )
                        reject(false)
                        return
                    }

                    resolve({ ...toRaw(formState) })
                } else {
                    reject(false)
                }
            })
        }
    })
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
    passwordVisible.value = !passwordVisible.value
}

// 复制密码
const copyPassword = async () => {
    if (formState.password) {
        try {
            await navigator.clipboard.writeText(formState.password)
            message.success(t('Password copied to clipboard'))
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = formState.password
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            message.success(t('Password copied to clipboard'))
        }
    }
}

defineExpose({ submitRules, clearValidate, resetFields, forceUpdateEditData })
</script>

<style lang="less" scoped>
.add-sub-account {
    :deep(.ant-form) {
        .ant-form-item-has-success {
            margin-bottom: 24px !important;
        }

        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }

        .ant-form-item {
            font-size: 14px;
            margin-bottom: 24px;

            .ant-form-item-label {
                width: 100px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: var(--text-60);

                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
            }

            .ant-form-item-control-input {
                min-height: 32px;

                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 11px;
                        font-size: 14px;

                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }

                    .ant-switch-checked {
                        background-color: var(--themeColor);
                    }
                }
            }

            .ant-switch {
                background-color: rgba(34, 34, 34, 0.04);
                min-width: 44px;
                height: 22px;
                line-height: 20px;
                font-size: 14px;

                &::after {
                    width: 18px;
                    height: 18px;
                    border-radius: 18px;
                    top: 1px;
                    left: 1px;
                }

                .ant-switch-inner {
                    margin-right: 6px;
                    margin-left: 24px;
                    font-size: 12px;
                }

                &:focus {
                    box-shadow: none;
                }
            }

            .ant-switch-checked {
                &::after {
                    left: 100%;
                    margin-left: -1px;
                    -webkit-transform: translateX(-100%);
                    transform: translateX(-100%);
                }
            }
        }
    }

    .p-title {
        font-size: 12px;
        padding-left: 14px;
        color: var(--text-80);
    }
}

.password-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    line-height: 1;
    color: var(--input-color);
    .copy-icon {
        line-height: 1;
        margin-right: 8px;
        display: flex;
        align-items: center;
    }
}

:deep(.el-form-item__label) {
    color: var(--text-60);
}
:deep(.el-input.is-disabled) {
    opacity: 0.7;
}
:deep(.el-select.disbaled) {
    cursor: not-allowed;
    opacity: 0.7;
}
:deep(.el-select__wrapper.is-disabled .el-select__selected-item) {
    opacity: 0.45;
}
:deep(.el-input--prefix .el-input__wrapper) {
    padding-left: 1px;
}
</style>
