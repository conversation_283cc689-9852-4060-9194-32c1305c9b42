<template>
    <a-card
        :bordered="false"
        :class="`${className} bg-ff dark:bg-ff-dark rounded card-p`"
    >
        <a-spin :spinning="spinning">
            <div class="margin-b-2 flex items-center">
                <slot name="title">
                    <slot name="icon"></slot>
                    <h5 class="margin-l-2 font-title">{{ title }}</h5>
                </slot>
            </div>
            <p class="textContent margin-b-2">
                <span class="margin-r-1 text3xl font-bold">{{ quantity }}</span
                ><span class="unit-title">{{ unit }}</span>
            </p>
            <div class="cardDetail flex justify-arround radius-1 pd">
                <div class="flex flex-col margin4 blockContentLeft">
                    <slot name="contentLeft"></slot>
                </div>
                <div class="flex flex-col justify-arround blockContentRight">
                    <slot name="contentRight"></slot>
                </div>
            </div>
        </a-spin>
    </a-card>
</template>
<script>
import { reactive, toRefs, onMounted } from 'vue'
import { Divider } from 'ant-design-vue'

export const AbstractCard = {
    name: `AbstractCard`,
    components: { Divider },
    props: {
        className: String,
        title: { type: String, required: true },
        unit: { type: String, required: true },
        quantity: { type: Number, required: true },
        spinning: { type: Boolean, default: () => false },
    },
    setup() {
        return {}
    },
}

export default AbstractCard
</script>
<style lang="less" scoped>
.card-p {
    :deep(.ant-card-body) {
        padding: 16px !important;
        border-radius: 4px;
    }
}
.textContent {
    color: #222222;
    padding-left: 24px;
    box-sizing: border-box;
}
.cardDetail {
    background: #f5f7f7;
    color: #222222;
    .blockContentLeft {
        width: 34%;
        position: relative;
        font-family: AlibabaPuHuiTi_2_55_Regular !important;
        &::after {
            position: absolute;
            right: 0px;
            content: ' ';
            display: inline-block;
            width: 1px;
            height: 100%;
            top: 0;
            background: rgba(34, 34, 34, 0.08);
        }
    }

    .blockContentRight {
        font-family: AlibabaPuHuiTi_2_55_Regular !important;
    }
}

.font-title {
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular !important;
}

.unit-title {
    color: rgba(34, 34, 34, 0.65);
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
}
@media screen and (max-width: 1600px) {
    .unit-title {
        font-size: 12px;
    }
}

.margin-b-2 {
    margin-bottom: 8px;
}

.margin-l-2 {
    margin-left: 8px;
}

.margin-r-2 {
    margin-right: 8px;
}

.margin-r-3 {
    margin-right: 12px;
}

.margin-r-1 {
    margin-right: 4px;
}

.text3xl {
    font-size: 32px;
    color: #222222;
    line-height: 40px;
}

.margin4 {
    margin-right: 16px;
}

.pd {
    padding: 8px 16px;
}
</style>
