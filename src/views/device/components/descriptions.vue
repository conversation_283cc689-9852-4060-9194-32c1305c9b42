
<template>
    <div :style="objectStyle" class="flex flex-wrap place-content-start">
        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">soh：</div>
            <div>
                {{ data?.soh || data?.soh == 0 ? data?.soh + ' %' : '-' }}
            </div>
        </div>
        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">实时功率：</div>
            <div>
                {{
                    data?.realtimePower || data?.realtimePower == 0
                        ? accountUnit(data?.realtimePower, 1000)
                        : '-'
                }}
            </div>
        </div>
        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">累计充电量：</div>
            <div>
                {{
                    data?.totalChargeQuantity || data?.totalChargeQuantity == 0
                        ? accountUnit(data?.totalChargeQuantity, 1000, 'h')
                        : '-'
                }}
            </div>
        </div>
        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">soc：</div>
            <div>
                {{ data?.soc || data?.soc == 0 ? data?.soc + ' %' : '-' }}
            </div>
        </div>

        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">实时电压：</div>
            <div>
                {{
                    data?.totalVoltage || data?.totalVoltage == 0
                        ? data?.totalVoltage + ' V'
                        : '-'
                }}
            </div>
        </div>

        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">累计放电量：</div>
            <div>
                {{
                    data.totalDischargeQuantity ||
                    data.totalDischargeQuantity == 0
                        ? accountUnit(data.totalDischargeQuantity, 1000, 'h')
                        : '-'
                }}
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit } from '../const'
export default {
    name: 'descriptions',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '1000%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup() {
        return {
            accountUnit,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;
    border-right-style: solid;
    border-right-color: rgba(34, 34, 34, 0.08);
    border-right-width: 1px;
    color: #222222;
    font-size: 13px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    .descriptions-item-label {
        padding-left: 2rem;
    }

    &:nth-child(3n) {
        border-right-color: #fff !important;
    }
}
</style>