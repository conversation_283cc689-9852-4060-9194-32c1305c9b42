<template>
    <div :style="objectStyle" class="flex flex-wrap place-content-start pt-6">
        <div class="descriptions-item mb-5">
            <div class="descriptions-item-label">
                {{ $t('zhengxiangyougongzongdianneng') }}：
            </div>
            <div class="descriptions-content">
                {{
                    data?.epp || data.epp == 0
                        ? accountUnit(data.epp, 1000, 'h')
                        : '-'
                }}
            </div>
        </div>
        <div class="descriptions-item mb-5">
            <div class="descriptions-item-label">
                {{ $t('fanxiangyougongzongdianneng') }}：
            </div>
            <div class="descriptions-content">
                {{
                    data?.epn || data.epn == 0
                        ? accountUnit(data.epn, 1000, 'h')
                        : '-'
                }}
            </div>
        </div>
        <div class="descriptions-item mb-5">
            <div class="descriptions-item-label">
                {{ $t('dianyabianbi') }}：
            </div>
            <div class="descriptions-content">
                {{ data?.pt || data?.pt == 0 ? data?.pt : '-' }}
            </div>
        </div>
        <div class="descriptions-item mb-5">
            <div class="descriptions-item-label">
                {{ $t('dianliubianbi') }}：
            </div>
            <div class="descriptions-content">
                {{ data?.ct || data?.ct == 0 ? data?.ct : '-' }}
            </div>
        </div>
        <div class="descriptions-item mb-2">
            <div class="descriptions-item-label">
                {{ $t('zongyougonggonglv') }}：
            </div>
            <div class="descriptions-content">
                {{ data?.p || data.p == 0 ? accountUnit(data.p, 1000) : '-' }}
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit } from '../const'
import { useI18n } from 'vue-i18n'
export default {
    name: 'energyStorage',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup() {
        const { t, locale } = useI18n()
        return {
            accountUnit,
            t,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 50%;
    display: flex;
    border-right-style: solid;
    border-right-color: var(--border);
    border-right-width: 1px;

    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    .descriptions-item-label {
        padding-left: 16px;
        width: 41%;
        color: var(--text-60);
    }

    .descriptions-content {
        color: var(--text-100);
    }

    &:nth-child(2n) {
        border-right-color: transparent !important;
    }

    .pt-6 {
        padding-top: 24px !important;
    }

    .mb-5 {
        margin-bottom: 20px !important;
    }
}
</style>