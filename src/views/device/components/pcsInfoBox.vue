<template>
    <div class="pt-6">
        <div class="ul-box">
            <ul class="flex bg-title-t pl-4">
                <li class="pr-4">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('station_yunxingzhuangtai') }}：</span
                    ><span
                        class="origin"
                        v-if="data?.errorStatus || data.errorStatus == 0"
                    ></span
                    ><span class="ml-1 text-sm" style="color: var(--text-80)">{{
                        data?.errorStatus || data?.errorStatus == 0
                            ? errorStatus[data?.errorStatus]
                            : '-'
                    }}</span>
                </li>
                <li class="pr-4">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('station_bingwangzhuangtai') }}：</span
                    ><span
                        class="ml-1 text-sm text-primary-text dark:text-80-dark"
                        >{{
                            data?.onGridStatus || data.onGridStatus == 0
                                ? onGridStatus[data.onGridStatus]
                                : '-'
                        }}</span
                    >
                </li>
                <li class="pr-4">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('station_chongfangdianzhuangtai') }}：</span
                    ><span
                        class="ml-1 text-sm text-primary-text dark:text-80-dark"
                        >{{
                            data?.chargeStatus || data?.chargeStatus == 0
                                ? batteryStatus[data.chargeStatus]
                                : '-'
                        }}</span
                    >
                </li>
                <li class="pr-4">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('staion_igbtwendu') }}：
                        <span class="text-primary-text dark:text-80-dark">
                            {{
                                data?.igbtTemp || data?.igbtTemp == 0
                                    ? data?.igbtTemp + ' °C'
                                    : '-'
                            }}
                        </span>
                    </span>
                </li>
            </ul>
        </div>

        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('zongyougonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.p || data.p == 0 ? accountUnit(data.p, 1000) : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('zongwugonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.s || data?.s == 0 ? data?.s + ' kvar' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('zongshizaigonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.q || data?.q == 0 ? data?.q + ' kVA' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Axiangyougonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.pa || data.pa == 0
                            ? accountUnit(data.pa, 1000)
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Bxiangyougonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.pb || data.pb == 0
                            ? accountUnit(data.pb, 1000)
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Cxiangyougonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.pc || data.pc == 0
                            ? accountUnit(data.pc, 1000)
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Axiangwugonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.sa || data?.sa == 0 ? data?.sa + ' kvar' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Bxiangwugonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.sb || data?.sb == 0 ? data?.sb + ' kvar' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Cxiangwugonggonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.sc || data?.sc == 0 ? data?.sc + ' kvar' : '-' }}
                </div>
            </div>

            <div class="descriptions-item mb-2">
                <div class="descriptions-item-label">
                    {{ $t('Axiangzongshizaigonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.qa || data?.qa == 0 ? data?.qa + ' kVA' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Bxiangzongshizaigonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.qb || data?.qb == 0 ? data?.qb + ' kVA' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Cxiangzongshizaigonglv') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.qc || data?.qc == 0 ? data?.qc + ' kVA' : '-' }}
                </div>
            </div>

            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Axiangdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.ua || data?.ua == 0 ? data?.ua + ' V' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Bxiangdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.ub || data?.ub == 0 ? data?.ub + ' V' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Cxiangdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.uc || data?.uc == 0 ? data?.uc + ' V' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Axiangdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.ia || data?.ia == 0 ? data?.ia + ' A' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Bxiangdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.ib || data?.ib == 0 ? data?.ib + ' A' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-4">
                <div class="descriptions-item-label">
                    {{ $t('Cxiangdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.ic || data?.ic == 0 ? data?.ic + ' A' : '-' }}
                </div>
            </div>

            <div class="descriptions-item mb-2">
                <div class="descriptions-item-label">
                    {{ $t('gonglvyinshu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.df || data?.df == 0 ? data?.df : '-' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, errorStatus, onGridStatus, batteryStatus } from '../const'
import { useI18n } from 'vue-i18n'
export default {
    name: 'pcsInfoBox',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '1000%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup() {
        const { t } = useI18n()
        return {
            accountUnit,
            errorStatus,
            onGridStatus,
            batteryStatus,
            t,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;
    border-right-style: solid;
    border-right-color: rgba(34, 34, 34, 0.08);
    border-right-width: 1px;

    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    .descriptions-item-label {
        padding-left: 24px;
        width: 55%;
        color: var(--text-60);
    }

    .descriptions-content {
        color: var(--text-100);
    }

    &:nth-child(3n) {
        border-right-color: transparent !important;
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
}

.ul-box {
    height: 40px;
    line-height: 40px;
    background: var(--bg-f5);
}

.pt-6 {
    padding-top: 24px !important;
}

.pl-4 {
    padding-left: 16px !important;
}

.text-sm {
    font-size: 14px !important;
    line-height: 20px !important;
}

.ml-1 {
    margin-left: 4px !important;
}
</style>
