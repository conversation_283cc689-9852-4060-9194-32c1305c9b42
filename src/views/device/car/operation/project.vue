<template>
    <div class="custom-table flex flex-col">
        <!-- 列设置按钮 -->
        <div class="table-header flex justify-end gap-x-4 mb-4">
            <div class="flex items-center">
                <el-input v-model="searchValueP" class="">
                    <template #prepend>
                        <el-select
                            v-model="searchTypeP"
                            style="width: 112px"
                            @change="onChangeSearchTypeP"
                        >
                            <el-option
                                v-for="item in searchTypesP"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                    <template #append>
                        <el-button plain :icon="Search" @click="onSearchP" />
                    </template>
                </el-input>
            </div>
            <el-button @click="showColumnSelector" plain round>
                {{ $t('Custom Columns') }}
                <iconSvg name="customList" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button @click="addProject" plain round>
                {{ $t('Add project')
                }}<iconSvg name="addDevice" :class="'w-4 h-4 ml-1'" />
            </el-button>
        </div>
        <div class="flex-1 h-0 overflow-y-auto">
            <!-- 表格主体 -->
            <div class="w-full h-full">
                <el-auto-resizer>
                    <template #default="{ height, width }">
                        <el-table-v2
                            :columns="visibleColumns"
                            :data="tableData"
                            :width="width"
                            :height="height"
                            v-model:sort-state="sortState"
                            @column-sort="handleSortChange"
                            fixed
                            :row-height="70"
                            :row-event-handlers="{ onClick: rowClick }"
                        >
                            <template #empty>
                                <empty-data
                                    :description="$t('zanwushuju')"
                                    style="margin-top: 80px"
                                >
                                    <slot name="empty"></slot>
                                </empty-data>
                            </template>
                        </el-table-v2>
                    </template>
                </el-auto-resizer>
            </div>
        </div>
        <div class="flex justify-center mt-4">
            <el-pagination
                background
                :page-sizes="[10, 20, 30, 40]"
                layout="prev, pager, next, sizes"
                :total="projectPageTotal"
                v-model:current-page="projectPageInfo.current"
                :page-size="projectPageInfo.size"
                @change="projectPageChange"
                @size-change="handleProjectSizeChange"
            />
        </div>

        <!-- 列选择器弹窗 -->
        <el-drawer
            v-model="dialogVisible"
            :size="500"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header text-title dark:text-title-dark">
                        <span>{{ $t('column_settings') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button plain round @click="onSave" type="primary">{{
                            $t('Confirm')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div class="flex items-start h-full">
                <div class="flex-1">
                    <div class="flex justify-start items-center">
                        <el-checkbox
                            v-model="checkAll"
                            :indeterminate="isIndeterminate"
                            @change="handleCheckAllChange"
                            style="width: auto"
                        >
                            {{ $t('Select all') }}
                            {{
                                checkedColumns.length
                                    ? '(' + checkedColumns.length + ')'
                                    : ''
                            }}
                        </el-checkbox>
                        <!-- <div
                            class="ml-4 text-title dark:text-title-dark cursor-pointer select-none"
                            @click="onInvert"
                        >
                            反选
                        </div> -->
                    </div>
                    <el-checkbox-group
                        v-model="checkedColumns"
                        @change="onChangeCheckedColumns"
                        class="column-selector"
                    >
                        <el-checkbox
                            v-for="(item, index) in columnList"
                            :value="item.key"
                            :key="item.key"
                            :disabled="index < 2"
                            class="pl-4 relative"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>
                            {{ item.title }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
                <el-divider direction="vertical" style="height: 100%" />
                <div class="flex-1">
                    <draggable
                        v-model="draggabledColumns"
                        item-key="key"
                        :disabled="false"
                        handle=".drag-handle"
                        @end="handleDragEnd"
                        :move="checkMove"
                    >
                        <template #item="{ element, index }">
                            <div class="column-item">
                                <el-icon
                                    class="drag-handle-disabled"
                                    v-if="index < 2"
                                >
                                    <iconSvg name="lock" class="w-5 h-5" />
                                </el-icon>
                                <el-icon class="drag-handle" v-else>
                                    <iconSvg name="drag" class="w-5 h-5" />
                                </el-icon>
                                <div
                                    class="text-title dark:text-title-dark flex-1"
                                    :class="
                                        index < 2
                                            ? ' opacity-40 select-none cursor-not-allowed'
                                            : ''
                                    "
                                >
                                    {{ element.title }}
                                </div>
                                <div
                                    class="w-6 h-6 flex items-center justify-center text-title dark:text-title-dark"
                                    :class="
                                        index < 2
                                            ? ' opacity-60 cursor-not-allowed'
                                            : 'cursor-pointer'
                                    "
                                    @click="onDeleteItem(element)"
                                >
                                    <el-icon size="14">
                                        <CloseBold />
                                    </el-icon>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="addVisible"
            :size="486"
            :show-close="false"
            @close="cancelAdd"
            :close-on-click-modal="false"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{
                            isEdit ? $t('Project detail') : $t('Add project')
                        }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelAdd">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmAdd()"
                            >{{
                                isEdit ? $t('Save') : $t('Confirm')
                            }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="forms">
                <el-form
                    ref="formRef"
                    :model="formState"
                    :rules="rules"
                    :label-width="labelWidth"
                    label-position="left"
                    hide-required-asterisk
                >
                    <el-form-item
                        :label="$t('Project name')"
                        prop="projectName"
                    >
                        <el-input
                            v-model="formState.projectName"
                            :placeholder="$t('placeholder_qingshuru')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Project No')" prop="projectNo">
                        <el-input
                            v-model="formState.projectNo"
                            :placeholder="$t('placeholder_qingshuru')"
                            :maxlength="20"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Device model')" prop="model">
                        <el-select
                            v-model="formState.model"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in modelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                            <template #header>
                                <el-input
                                    v-model="modelOptionsSearchValue"
                                    style="width: 100%"
                                    :placeholder="
                                        $t('Search or create a new option')
                                    "
                                    @input="onSearchM"
                                />
                            </template>
                            <template
                                #footer
                                v-if="modelOptionsSearchValue.length > 0"
                            >
                                <div
                                    class="pl-2.5 cursor-pointer select-none text-primary-text dark:text-80-dark"
                                    @click="addNewOption"
                                >
                                    {{ $t('Create new options') }}：{{
                                        modelOptionsSearchValue
                                    }}
                                </div>
                            </template>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Vehicle type')"
                        prop="vehicleType"
                    >
                        <el-select
                            v-model="formState.vehicleType"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in vehicleTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('Power type')" prop="powerType">
                        <el-select
                            v-model="formState.powerType"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in powerTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('Cell type')" prop="cellType">
                        <el-select
                            v-model="formState.cellType"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in cellTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('CellPackaging')" prop="cellPack">
                        <el-select
                            v-model="formState.cellPack"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in cellPackOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Battery series count')"
                        prop="series"
                    >
                        <el-input-number
                            controls-position="right"
                            v-model="formState.series"
                            :min="1"
                            :max="50"
                            :precision="0"
                            class="w-full"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('Battery parallel count')"
                        prop="parallels"
                    >
                        <el-input-number
                            controls-position="right"
                            v-model="formState.parallels"
                            :min="1"
                            :max="10"
                            :precision="0"
                            class="w-full"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Battery box count')" prop="tanks">
                        <el-input-number
                            controls-position="right"
                            v-model="formState.tanks"
                            :min="1"
                            :max="10"
                            :precision="0"
                            class="w-full"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('Individual voltage')"
                        prop="cellVoltage"
                    >
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.cellVoltage"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="w-full fixed-input"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                V
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item :label="$t('Rated power')" prop="ratedPower">
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.ratedPower"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="flex-1"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                kW
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="$t('RatedTotalVoltage')"
                        prop="ratedVoltage"
                    >
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.ratedVoltage"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="flex-1"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                V
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Rated current')"
                        prop="ratedCurrent"
                    >
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.ratedCurrent"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="flex-1"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                A
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Rated capacity')"
                        prop="ratedCapacity"
                    >
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.ratedCapacity"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="flex-1"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                Ah
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Rated energy')"
                        prop="ratedEnergy"
                    >
                        <div class="w-full relative">
                            <el-input-number
                                controls-position="right"
                                v-model="formState.ratedEnergy"
                                :precision="2"
                                :min="0"
                                :max="10000"
                                class="flex-1"
                            />
                            <div
                                class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                            >
                                kWh
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item
                        :label="$t('Initiation date')"
                        prop="createDate"
                    >
                        <el-date-picker
                            v-model="formState.createDate"
                            type="date"
                            :placeholder="$t('placeholder_qingxuanze')"
                            value-format="YYYY-MM-DD"
                            class="w-full"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('Project description')"
                        prop="description"
                    >
                        <el-input
                            v-model="formState.description"
                            type="textarea"
                            :maxlength="200"
                            :rows="4"
                            :placeholder="$t('placeholder_qingshuru')"
                        />
                    </el-form-item>
                </el-form>
            </div>
        </el-drawer>
    </div>
</template>

<script setup>
import {
    ref,
    computed,
    watch,
    reactive,
    onMounted,
    toRaw,
    h,
    nextTick,
} from 'vue'
import { ElPopover, ElCheckbox, ElButton, ElIcon } from 'element-plus'
import {
    Setting,
    Operation,
    Search,
    CloseBold,
    Filter,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import powerApi from '@/apiService/power'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { TableV2SortOrder } from 'element-plus'
import iconSvg from '@/components/svgIcon'
import Decimal from 'decimal.js'
const { t, locale } = useI18n()
const store = useStore()
const router = useRouter()
// Props定义

// 表格数据
const tableData = ref([])

const projectPageTotal = ref(0)
const projectPageInfo = ref({
    current: 1,
    size: 10,
})
const getProjectData = async () => {
    const { sortField, sortOrder } = sortBy.value
    let params = {
        status: undefined,
        model: modelFilter.value || undefined,
        vehicleType: vehicleTypeFilter.value || undefined,
        cellType: cellTypeFilter.value || undefined,
        powerType: powerTypeFilter.value || undefined,
        projectId: undefined,
        customerId: undefined,
        ...projectPageInfo.value,
        sortField: sortField == 'createDate' ? 'createTime' : sortField,
        sortOrder,
    }
    if (searchValueP.value) {
        if (searchTypeP.value == 'no') {
            params.projectNo = searchValueP.value
            params.projectName = undefined
        } else if (searchTypeP.value == 'name') {
            params.projectName = searchValueP.value
            params.projectNo = undefined
        }
    } else {
        params.projectName = undefined
        params.projectNo = undefined
    }

    let res = await powerApi.getProjectPageList(params)
    tableData.value = res.data.data.records.map((item) => {
        return {
            ...item,
            ...item.bmsSummaryInfo,
            totalCapacity: new Decimal(item.bmsSummaryInfo?.totalDevices).mul(
                new Decimal(item.ratedPower)
            ),
        }
    })
    projectPageTotal.value = res.data.data.total
}

// watch(
//     () => props.data,
//     (newVal, oldVal) => {
//         tableData.value = newVal
//     },
//     { immediate: true, deep: true }
// )
// 列配置相关
const sortState = ref({
    createDate: TableV2SortOrder.NONE,
})
const modelFilter = ref('')
const selectedModels = ref([])
const vehicleTypeFilter = ref([])
const cellTypeFilter = ref([])
const powerTypeFilter = ref([])

const columnList = ref([
    {
        title: t('Project name'),
        dataKey: 'projectName',
        key: 'projectName',
        width: 280,
        fixed: true,
        cellRenderer: ({ rowData }) => {
            return h('div', { class: 'flex flex-col' }, [
                h(
                    'div',
                    { class: 'text-primary-text dark:text-80-dark' },
                    rowData.projectName
                ),
                h(
                    'div',
                    { class: ' secondar-text dark:text-60-dark' },
                    t('Project No') + '：' + rowData.projectNo
                ),
            ])
        },
    },
    {
        title: t('Main customer'),
        dataKey: 'majorCustomers',
        key: 'majorCustomers',
        width: 140,
    },
    {
        title: t('Total devices'),
        dataKey: 'totalDevices',
        key: 'totalDevices',
        width: 108,
        // sortable: true,  // 不支持
    },
    {
        title: t('Activated devices'),
        dataKey: 'activeCount',
        key: 'activeCount',
        width: 124,
        // sortable: true,  // 不支持
    },
    {
        title: t('Online devices'),
        dataKey: 'onlineCount',
        key: 'onlineCount',
        width: 124,
        // sortable: true,  // 不支持
    },
    {
        title: t('Device model'),
        dataKey: 'model',
        key: 'model',
        width: 144,
    },
    {
        title: t('Vehicle type'),
        dataKey: 'vehicleType',
        key: 'vehicleType',
        width: 120,
        cellRenderer: ({ rowData }) => {
            return getVehicleType(rowData.vehicleType)
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                vehicleTypeFilter.value = selectedVehicleTypes.value
                getProjectData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedVehicleTypes.value = []
                vehicleTypeFilter.value = []
                getProjectData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-xs' }, t('Vehicle type')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        (vehicleTypeOptions.value || []).map(
                                            (item) =>
                                                h(
                                                    ElCheckbox,
                                                    {
                                                        modelValue:
                                                            selectedVehicleTypes.value.includes(
                                                                item.value
                                                            ),
                                                        'onUpdate:modelValue': (
                                                            val
                                                        ) => {
                                                            if (val) {
                                                                selectedVehicleTypes.value.push(
                                                                    item.value
                                                                )
                                                            } else {
                                                                const index =
                                                                    selectedVehicleTypes.value.indexOf(
                                                                        item.value
                                                                    )
                                                                if (
                                                                    index > -1
                                                                ) {
                                                                    selectedVehicleTypes.value.splice(
                                                                        index,
                                                                        1
                                                                    )
                                                                }
                                                            }
                                                        },
                                                    },
                                                    () => item.label
                                                )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (vehicleTypeFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('Cell type'),
        dataKey: 'cellType',
        key: 'cellType',
        width: 144,
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                cellTypeFilter.value = selectedCellTypes.value
                getProjectData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedCellTypes.value = []
                cellTypeFilter.value = []
                getProjectData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-xs' }, t('Cell type')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        (cellTypeOptions.value || []).map(
                                            (item) =>
                                                h(
                                                    ElCheckbox,
                                                    {
                                                        modelValue:
                                                            selectedCellTypes.value.includes(
                                                                item.value
                                                            ),
                                                        'onUpdate:modelValue': (
                                                            val
                                                        ) => {
                                                            if (val) {
                                                                selectedCellTypes.value.push(
                                                                    item.value
                                                                )
                                                            } else {
                                                                const index =
                                                                    selectedCellTypes.value.indexOf(
                                                                        item.value
                                                                    )
                                                                if (
                                                                    index > -1
                                                                ) {
                                                                    selectedCellTypes.value.splice(
                                                                        index,
                                                                        1
                                                                    )
                                                                }
                                                            }
                                                        },
                                                    },
                                                    () => item.label
                                                )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (cellTypeFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('Power type'),
        dataKey: 'powerType',
        key: 'powerType',
        width: 144,
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                powerTypeFilter.value = selectedPowerTypes.value
                getProjectData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedPowerTypes.value = []
                powerTypeFilter.value = []
                getProjectData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-xs' }, t('Power type')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        (powerTypeOptions.value || []).map(
                                            (item) =>
                                                h(
                                                    ElCheckbox,
                                                    {
                                                        modelValue:
                                                            selectedPowerTypes.value.includes(
                                                                item.value
                                                            ),
                                                        'onUpdate:modelValue': (
                                                            val
                                                        ) => {
                                                            if (val) {
                                                                selectedPowerTypes.value.push(
                                                                    item.value
                                                                )
                                                            } else {
                                                                const index =
                                                                    selectedPowerTypes.value.indexOf(
                                                                        item.value
                                                                    )
                                                                if (
                                                                    index > -1
                                                                ) {
                                                                    selectedPowerTypes.value.splice(
                                                                        index,
                                                                        1
                                                                    )
                                                                }
                                                            }
                                                        },
                                                    },
                                                    () => item.label
                                                )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (powerTypeFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('Region'),
        dataKey: 'city',
        key: 'city',
        width: 124,
    },
    {
        title: t('Total capacity') + '(Ah)',
        dataKey: 'totalCapacity',
        key: 'totalCapacity',
        width: 124,
        // sortable: true,  // 不支持
    },
    {
        title: t('Total power') + '(Ah)',
        dataKey: 'ratedPower',
        key: 'ratedPower',
        width: 124,
    },
    {
        title: t('Charging duration') + '(h)',
        dataKey: 'chgTimeSum',
        key: 'chgTimeSum',
        width: 124,
    },
    {
        title: t('Charging amount') + '(Ah)',
        dataKey: 'chgCapSum',
        key: 'chgCapSum',
        width: 124,
    },
    {
        title: t('Discharging duration') + '(h)',
        dataKey: 'dsgTimeSum',
        key: 'dsgTimeSum',
        width: 124,
    },
    {
        title: t('Discharging amount') + '(Ah)',
        dataKey: 'dsgCapSum',
        key: 'dsgCapSum',
        width: 124,
    },
    {
        title: t('Alarm count'),
        dataKey: 'totalAlarms',
        key: 'totalAlarms',
        width: 124,
        // sortable: true,   // 不支持
    },
    {
        title: t('Initiation date'),
        dataKey: 'createDate',
        key: 'createDate',
        width: 124,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-xs' }, column.title),
                getSortIcon('createDate', sortState),
            ])
        },
    },
])

const checkedColumns = ref(columnList.value.map((col) => col.key))
const selectedColumns = ref([
    'projectName',
    'majorCustomers',
    'totalDevices',
    'model',
    'vehicleType',
    'cellType',
    'powerType',
    'chgCapSum',
    'dsgCapSum',
    'createDate',
])
const dialogVisible = ref(false)
const draggabledColumns = ref(columnList.value)
// 计算可见的列
function getResultArr(arr, rulesArr) {
    const map = arr.reduce((acc, item) => {
        acc[item.key] = item
        return acc
    }, {})
    return rulesArr.map((key) => map[key]).filter((item) => item !== undefined) // 过滤掉不存在的项
}
const visibleColumns = computed(() => {
    return getResultArr(columnList.value, selectedColumns.value)
})

const onChangeCheckedColumns = (e) => {
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    if (e.length == columnList.value.length) {
        checkAll.value = true
        isIndeterminate.value = false
    } else if (e.length == 0) {
        checkAll.value = false
        isIndeterminate.value = false
    } else {
        isIndeterminate.value = true
    }
}
// 显示列选择器
const showColumnSelector = () => {
    checkedColumns.value = visibleColumns.value.map((col) => col.key)
    draggabledColumns.value = visibleColumns.value.map((col) => col)
    dialogVisible.value = true
}

// 定义排序状态枚举
const SortOrder = {
    NONE: undefined,
    ASC: 'asc',
    DESC: 'desc',
}

// 获取下一个排序状态
const getNextSortOrder = (currentOrder) => {
    // 如果当前状态是 undefined，则切换到 'asc'
    if (!currentOrder) {
        return SortOrder.ASC
    }
    // 如果当前状态是 'asc'，则切换到 'desc'
    if (currentOrder === SortOrder.ASC) {
        return SortOrder.DESC
    }
    // 如果当前状态是 'desc'，则切换到 undefined
    if (currentOrder === SortOrder.DESC) {
        return SortOrder.NONE
    }
    // 默认返回 undefined
    return SortOrder.NONE
}

// 处理排序变化
const sortBy = ref({
    sortField: undefined,
    sortOrder: undefined,
})

const handleSortChange = ({ key, order }) => {
    console.log('Current sort state:', key, order)

    // 重置其他列的排序状态为默认
    Object.keys(sortState.value).forEach((k) => {
        if (k !== key) {
            sortState.value[k] = SortOrder.NONE
        }
    })

    // 获取当前列的排序状态
    const currentOrder = sortState.value[key]

    // 获取下一个排序状态
    const nextOrder = getNextSortOrder(currentOrder)
    console.log('Current order:', currentOrder, 'Next sort order:', nextOrder)

    // 更新排序状态
    sortState.value[key] = nextOrder

    // 构建排序参数
    sortBy.value =
        nextOrder !== SortOrder.NONE
            ? {
                  sortField: key,
                  sortOrder: nextOrder,
              }
            : {
                  sortField: undefined,
                  sortOrder: undefined,
              }

    // 更新请求参数并重新获取数据
    getProjectData()
}

// 修改所有可排序列的 headerCellRenderer，添加默认状态图标
const getSortIcon = (columnKey, sortState) => {
    const currentOrder = sortState[columnKey]
    // 默认状态（undefined）显示 sort 图标
    if (!currentOrder) {
        return h(iconSvg, {
            name: 'sort',
            class: 'w-3.5 h-3.5 opacity-40',
        })
    }
    // 升序状态显示 asc 图标
    if (currentOrder === SortOrder.ASC) {
        return h(iconSvg, {
            name: 'asc',
            class: 'w-3.5 h-3.5',
        })
    }
    // 降序状态显示 desc 图标
    return h(iconSvg, {
        name: 'desc',
        class: 'w-3.5 h-3.5',
    })
}

// 处理拖拽结束
const handleDragEnd = ({
    newDraggableIndex,
    newIndex,
    oldDraggableIndex,
    oldIndex,
}) => {
    console.log(newDraggableIndex, newIndex, oldDraggableIndex, oldIndex)

    // 可以在这里处理拖拽结束后的逻辑
}
const checkMove = (e) => {
    // 获取拖拽后的目标索引
    const targetIndex = e.draggedContext.futureIndex
    // 如果目标位置是前两个，则禁止拖拽
    return targetIndex >= 2
}
const closeDrawer = () => {
    dialogVisible.value = false
}
const onSave = () => {
    console.log(draggabledColumns.value)
    selectedColumns.value = draggabledColumns.value.map((col) => col.key)
    dialogVisible.value = false
    ElMessage.success(t('caozuochenggong'))
    console.log(visibleColumns.value)
}
const checkAll = ref(true)
const isIndeterminate = ref(true)
const handleCheckAllChange = (val) => {
    checkedColumns.value = val
        ? columnList.value.map((col) => col.key)
        : ['projectName', 'majorCustomers']
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    isIndeterminate.value = false
}
const onInvert = () => {
    const requiredColumns = ['projectName', 'majorCustomers']
    const currentChecked = [...checkedColumns.value]
    const allColumns = columnList.value.map((col) => col.key)

    // 反选逻辑：保留必选项，其他项取反
    checkedColumns.value = [
        ...requiredColumns,
        ...allColumns.filter(
            (key) =>
                !requiredColumns.includes(key) && !currentChecked.includes(key)
        ),
    ]

    // 更新拖拽列表
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
}

const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const getVehicleType = (val) => {
    if (vehicleTypes.value.length == 0) return
    if (!val) return '-'
    return vehicleTypes.value.find((item) => item.value == val).label
}
const formatterType = (e) => {
    return getVehicleType(e.vehicleType)
}

// 添加
const formState = reactive({
    projectNo: '',
    projectName: '',
    model: '',
    vehicleType: '',
    powerType: '',
    cellType: '',
    cellPack: '',
    tanks: undefined,
    series: undefined,
    parallels: undefined,
    ratedPower: undefined,
    ratedVoltage: undefined,
    cellVoltage: undefined,
    ratedCurrent: undefined,
    ratedCapacity: undefined,
    ratedEnergy: undefined,
    description: '',
    createDate: dayjs().format('YYYY-MM-DD'),
})

const rules = {
    projectName: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    projectNo: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    model: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    vehicleType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    powerType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    cellType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    cellPack: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    tanks: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    series: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    parallels: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    ratedPower: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    ratedVoltage: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    cellVoltage: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    ratedCurrent: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    ratedCapacity: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    ratedEnergy: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
    ],
    description: [
        { max: 200, message: t('message_zuidazhichichangdu'), trigger: 'blur' },
    ],
}
const modelOptionsSearchValue = ref('')
const onSearchM = (e) => {}
const modelOptions = computed(() => {
    let arr = store.state.dictionary.dictionaries.powerBmsModel
    return arr.filter((item) => {
        return item.label.includes(modelOptionsSearchValue.value)
    })
})

const allModelOptions = computed(() => {
    return store.state.dictionary.dictionaries.powerBmsModel
})

const addNewOption = async () => {
    const params = {
        type: 'powerBmsModel',
        itemValue: modelOptionsSearchValue.value,
    }
    const res = await powerApi.addDictItem(params)
    if (res.data.data) {
        ElMessage.success(t('caozuochenggong'))
        store.commit('dictionary/SET_DICTIONARY', {
            type: 'powerBmsModel',
            data: undefined,
        })
    }
    try {
        await store.dispatch('dictionary/getDictionary', 'powerBmsModel')
        let re = modelOptions.value.find((item) => {
            return item.label == modelOptionsSearchValue.value
        })
        formState.model = re.value
    } catch (error) {
        console.error(error)
    } finally {
        //
    }
}

const vehicleTypeOptions = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)

const powerTypeOptions = computed(
    () => store.state.dictionary.dictionaries.powerType || []
)

const cellTypeOptions = computed(
    () => store.state.dictionary.dictionaries.cellType || []
)

const cellPackOptions = computed(
    () => store.state.dictionary.dictionaries.cellPack || []
)

const selectedVehicleTypes = ref([])
const selectedCellTypes = ref([])
const selectedPowerTypes = ref([])

onMounted(async () => {
    await getProjectData()
    store.dispatch('dictionary/getDictionary', 'powerBmsModel')
    store.dispatch('dictionary/getDictionary', 'vehicleType')
    store.dispatch('dictionary/getDictionary', 'powerType')
    store.dispatch('dictionary/getDictionary', 'cellType')
    store.dispatch('dictionary/getDictionary', 'cellPack')
})
const projectPageChange = async () => {
    await getProjectData()
}
const handleProjectSizeChange = (e) => {
    projectPageInfo.value.size = e
}
const onSearchP = async (e) => {
    await getProjectData(e)
}
const addVisible = ref(false)

const addProject = () => {
    addVisible.value = true
}
const cancelAdd = () => {
    formRef.value?.clearValidate()
    if (isEdit.value) {
        formRef.value?.resetFields()
        Object.keys(formState).forEach((key) => {
            formState[key] = void 0
        })
        isEdit.value = false
    }
    addVisible.value = false
    // isEdit.value = false
}
const formRef = ref(null)
const addLoading = ref(false)
const confirmAdd = async () => {
    //  需要添加编辑和保存两种操作的逻辑区分。

    if (!formRef.value) return
    formRef.value.validate().then(async () => {
        console.log('表单数据:', formState)
        let params = {
            ...toRaw(formState),
        }
        console.log(params)
        if (isEdit.value) {
            let res = await powerApi.editProject({
                ...params,
                id: projectId.value,
            })
            if (res.data.code === 0) {
                ElMessage.success(t('caozuochenggong'))
            }
        } else {
            let res = await powerApi.createProject(params)
            if (res.data.code === 0) {
                ElMessage.success(t('caozuochenggong'))
            }
        }

        // emit('search', { label: searchTypeP.value, value: searchValueP.value })  xxxx 刷新数据

        // return
        // 提交成功后关闭抽屉
        formRef.value?.clearValidate()
        formRef.value?.resetFields()
        cancelAdd()
        await getProjectData()
    })
}
const searchTypeP = ref('no')
const searchValueP = ref()
const onChangeSearchTypeP = () => {}
const searchTypesP = ref([
    { value: 'no', label: t('Project No') },
    {
        value: 'name',
        label: t('Project name'),
    },
])

const emit = defineEmits(['search'])

const supplierId = computed(() => {
    return store.state.device.selectSupplierInfo?.id
})
const isEdit = ref(false)
const projectId = ref()
const rowClick = ({ rowData }) => {
    console.log(rowData)
    if (rowData.totalDevices > 0) {
        router.push({
            name: 'equipmentDetail',
            query: {
                sn: rowData.sn,
                supplierId: supplierId.value,
                projectId: rowData.id,
            },
        })
    } else {
        isEdit.value = true
        Object.keys(formState).forEach((key) => {
            formState[key] = rowData[key]
        })
        projectId.value = rowData.id
        addVisible.value = true
        // ElMessage.error(t('该项目下暂无设备'))
    }
}
const labelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '72px' : locale.value == 'en' ? '158px' : '140px'
    return res
})

const onDeleteItem = (item) => {
    const index = draggabledColumns.value.findIndex(
        (col) => col.key === item.key
    )
    if (index < 2) {
        return
    }
    if (index > -1) {
        draggabledColumns.value.splice(index, 1)
        const checkedIndex = checkedColumns.value.indexOf(item.key)
        if (checkedIndex > -1) {
            checkedColumns.value.splice(checkedIndex, 1)
        }
    }
}
</script>

<style scoped lang="less">
.custom-table {
    width: 100%;
    height: 100%;
}

.filter-wrapper {
    padding: 8px;

    .filter-group {
        margin-bottom: 8px;
    }

    .filter-options {
        margin-bottom: 8px;

        :deep(.el-checkbox) {
            display: block;
            margin-right: 0;
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.column-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
    justify-content: space-between;
}

.drag-handle {
    cursor: move;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
}
.drag-handle-disabled {
    cursor: not-allowed;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
    opacity: 0.4;
}

:deep(.el-checkbox) {
    margin-right: 0;
    width: 100%;
}

:deep(.el-checkbox.is-disabled) {
    cursor: not-allowed;
}
:deep(.el-input-number) {
    width: 100%;
    text-align: left;
    .el-input__inner {
        text-align: left;
    }
}

:deep(.fixed-input .el-input__wrapper) {
    padding-right: 80px;
}
:deep(.el-table-v2__header-cell .el-icon) {
    display: block;
}
:deep(.hasFilter) {
    color: var(--themeColor);
}
:deep(.el-table-v2__header-cell) {
    .el-table-v2__sort-icon {
        display: none !important;
    }

    &.is-sortable {
        .el-table-v2__header-cell-content {
            position: relative;
            padding-right: 24px;

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        &.ascending .el-table-v2__header-cell-content::after {
            content: '';
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"/></svg>')
                no-repeat center;
            background-size: contain;
        }

        &.descending .el-table-v2__header-cell-content::after {
            content: '';
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/></svg>')
                no-repeat center;
            background-size: contain;
        }
    }
}
:deep(.el-button:focus-visible) {
    outline: none !important;
}
</style>
