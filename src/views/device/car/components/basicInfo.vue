<template>
    <div class="pl-3">
        <div class="ul-box flex justify-between items-center pb-3">
            <ul class="flex items-center flex-1 w-0 title-family px-2.5">
                <li class="mr-4" style="">
                    <div
                        class="text-sm text-secondar-text dark:text-60-dark mb-2"
                    >
                        {{ $t('Device No') }}：{{ data.bmsInfo?.sn }}

                        <span
                            class="descriptions-item-label ml-4"
                            :class="locale"
                        >
                            {{ $t('activation_date') }}：
                        </span>
                        <span class="descriptions-content">
                            {{
                                data.bmsInfo?.activeTime
                                    ? dayjs(data.bmsInfo?.activeTime).format(
                                          'YYYY/MM/DD'
                                      )
                                    : '-'
                            }}
                        </span>
                    </div>
                    <div class="text-sm text-secondar-text dark:text-60-dark">
                        {{ $t('客户名称') }}：-
                    </div>
                </li>
            </ul>
            <!-- <el-button plain round @click="lookBattery" linear disabled>{{
                    $t('设备二维码')
                }}</el-button> -->
            <!-- <div
                class="w-14 h-14 bg-f5 dark:bg-f5-dark"
                @click="lookBattery"
            ></div> -->
        </div>
        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Vehicle type') }}：
                </div>
                <div class="descriptions-content">
                    {{ getVehicleType(data.productModel?.vehicleType) }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('BatteryNo') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.batteryNo || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('car_leijixunhuancishu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.cycleCount
                            ? data.bmsInfo?.cycleCount
                            : '0'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Cell type') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.cellType }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Device model') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.model }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('charging time') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.chgTimeSum || 0 }} h
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('CellPackaging') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.cellPack }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('HWID') }}：
                </div>
                <div class="descriptions-content">
                    <el-tooltip
                        v-if="data.bmsInfo?.hwid"
                        class="box-item"
                        :effect="isDark ? 'dark' : 'light'"
                        :content="data.bmsInfo?.hwid"
                        placement="top-start"
                    >
                        <a class="tag cursor-pointer w-0">{{
                            data.bmsInfo?.hwid || '-'
                        }}</a>
                    </el-tooltip>
                    <span v-else>-</span>
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Discharging Time') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.dsgTimeSum || 0 }} h
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('BatteryNumber') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.tanks || 0 }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('IMEI') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.imei || '-' }}
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('AccumulatedChargingCapacity') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.chgCapSum || 0 }} Ah
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Individual voltage') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.productModel?.cellVoltage
                            ? data.productModel?.cellVoltage
                            : '0'
                    }}
                    V
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('ICCID') }}：
                </div>
                <div class="descriptions-content">
                    <el-tooltip
                        v-if="data.bmsInfo?.iccid"
                        class="box-item"
                        :effect="isDark ? 'dark' : 'light'"
                        :content="data.bmsInfo?.iccid"
                        placement="top-start"
                    >
                        <a class="tag cursor-pointer w-0">{{
                            data.bmsInfo?.iccid || '-'
                        }}</a>
                    </el-tooltip>
                    <span v-else>-</span>
                </div>
            </div>
            <div class="descriptions-item">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('AccumulatedDischargingCapacity') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.dsgCapSum || 0 }} Ah
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('RatedTotalVoltage') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.ratedVoltage || 0 }} V
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('software_version') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.softwareVersion || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('manufacture_date') }}：
                </div>
                <div class="descriptions-content">-</div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('rated_total_current') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.ratedCurrent || 0 }} A
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <!--  :class="locale" <div class="descriptions-item-label">
                    {{ $t('BMS出厂报告') }}：
                </div>
                <div class="descriptions-content">-</div> -->
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('service_expire_time') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.serviceExpireDate
                            ? dayjs(data.bmsInfo?.serviceExpireDate).format(
                                  'YYYY/MM/DD'
                              )
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('car_edingrongliang') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.ratedCapacity || 0 }} Ah
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label"></div>
                <div class="descriptions-content"></div>
            </div>
            <div class="descriptions-item mb-3"></div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('rated_energy') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.productModel?.ratedEnergy || 0 }} kWh
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, batteryStatus } from '../../const'
// import { getState } from '@/common/util.js'
import { computed, onMounted, ref } from 'vue'
import store from '@/store'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
export default {
    name: 'bmsBox',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props, { emit }) {
        const { t, locale } = useI18n()
        const lookBattery = () => {
            emit('lookBattery', true)
        }
        const vehicleTypeOptions = computed(() => {
            return store.state.dictionary.dictionaries.vehicleType || []
        })
        const getVehicleType = (val) => {
            if (vehicleTypeOptions.value.length == 0) return
            if (!val) return props.data.bmsInfo?.x
            return vehicleTypeOptions.value.find((item) => item.value == val)
                .label
        }

        onMounted(async () => {
            await store.dispatch('dictionary/getDictionary', 'vehicleType')
        })

        const chargeState = ref([
            {
                label: t('status_chongdianzhong'),
                name: t('status_chongdian'),

                value: '1',
                icon: 'icon-ica-dianchi-fangdian',
                color: '#73ADFF',
                backGroundColor: '#73ADFF',
            },
            {
                label: t('status_fangdianzhong'),
                name: t('status_fangdian'),
                value: '2',
                icon: 'icon-ica-dianchi-chongdian',
                color: '#33BE4F',
                backGroundColor: '#33BE4F',
            },
            {
                label: t('status_daiji'),
                name: t('status_daiji'),
                value: '0',
                icon: 'icon-ica-dianchi-yunhang',
                color: '#222222',
                backGroundColor: '#d9d9d9',
            },
            {
                label: t('status_lixian'),
                name: t('status_lixian'),
                value: '3',
                icon: 'icon-ica-dianchi-lixian',
                color: '#666666',
                backGroundColor: '#666666',
            },
        ])
        const getState = (status) => {
            const item = chargeState.value.find((s) => s.value == status) || {}
            return item
        }
        const isDark = computed(() => {
            return store.state.theme.isDark
        })

        return {
            accountUnit,
            batteryStatus,
            lookBattery,
            getState,
            getVehicleType,
            vehicleTypeOptions,
            dayjs,
            isDark,
            locale,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;

    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;

    .descriptions-item-label {
        color: var(--text-60);
        padding-left: 22px;
        // width: 60%;
        max-width: 70%;
        &.en {
            padding-left: 12px;
        }
    }

    .descriptions-content {
        color: var(--text-100);
        flex: 1;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &:nth-child(3n-2) {
        .descriptions-item-label {
            padding-left: 9px;
        }
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
    &.origin-gray {
        background-color: rgba(34, 34, 34, 0.5);
    }
}

.ul-box {
    // height: 40px;
    padding: 10px 0;
    // line-height: 40px;
    line-height: 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 16px;
    // background: var(--bg-f5);
}
.tag {
    // color: var(--themeColor);
}
</style>
