<template>
    <div ref="chartContainer" style="width: 100%; height: 330px"></div>
</template>

<script>
import { ref, onMounted, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { distributedOptions } from '@/views/device/const'
import { useI18n } from 'vue-i18n'
import { echartsColorVars, getThemeColor } from '@/common/util'
import { useStore } from 'vuex'
import useTheme from '@/common/useTheme'
export default {
    name: 'StatusDistributionChart',
    props: {
        chartData: {
            type: Array,
            default: () => [],
        },
        type: {
            type: String,
            default: 'month',
        },
    },
    setup(props) {
        const { t } = useI18n()
        const chartContainer = ref(null)
        let chart = null

        const initChart = () => {
            if (chartContainer.value) {
                chart = echarts.init(chartContainer.value)
                updateChart()
            }
        }

        const updateChart = () => {
            const { chartData } = props
            const xData = chartData.map((item) => item.x)
            const yData = chartData.map((item) => item.value)
            const option = distributedOptions(props.type)
            option.series[0].data = yData
            option.xAxis.data = xData
            let max = Math.max(...yData)
            if (max < 5) {
                option.yAxis.max = 5
            }
            chart && chart.setOption(option)
        }

        onMounted(() => {
            initChart()
        })

        watch(
            () => props.chartData,
            () => {
                updateChart()
            },
            { deep: true }
        )
        const store = useStore()
        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                console.log(isComplete, '111')
                updateChart()
            }
        })

        return { chartContainer }
    },
}
</script>

<style scoped>
/* 如果需要额外的样式,可以在这里添加 */
</style>
