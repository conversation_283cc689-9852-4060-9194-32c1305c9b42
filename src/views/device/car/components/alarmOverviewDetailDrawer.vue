<template>
    <el-drawer
        :model-value="visible"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ '异常详情' }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button plain round @click="onClose">关闭</el-button>
                    <el-button
                        plain
                        type="primary"
                        round
                        v-if="alarmFormData?.alarmStatus == 0 && false"
                        @click="onCleared(alarmId)"
                        >忽略异常</el-button
                    >
                </div>
            </div>
        </template>
        <div class="alarm-formData">
            <div class="flex flex-col">
                <div class="py-1">
                    <span class="title-l title-content">异常名称：</span>
                    <span class="title-r col">{{
                        alarmFormData?.alarmDesc
                    }}</span>
                </div>
                <div class="py-1 alarm-stauts">
                    <span class="title-l title-content">异常状态：</span>
                    <dictionary
                        :statusOptions="alarmStatusList"
                        :value="alarmFormData?.alarmStatus"
                        :color="'backGroundColor'"
                    />
                </div>
                <div class="py-1 alarm-level">
                    <span class="title-l title-content">异常级别：</span>

                    <dictionary
                        :statusOptions="alarmLevelList"
                        :value="alarmFormData?.alarmLevel"
                        :showBadge="false"
                    />
                </div>
                <!-- <div class="py-1 alarm-type">
                    <span class="title-l title-content">设备类型：</span>

                    <dictionary
                        :statusOptions="deviceTypeList"
                        :value="alarmFormData?.deviceType"
                        :showBadge="false"
                    />
                </div>
                <div class="py-1">
                    <span class="title-l title-content">设备名称：</span>
                    <span class="title-r col">{{
                        alarmFormData?.stationName
                    }}</span>
                </div> -->
                <div class="py-1">
                    <span class="title-l title-content">设备编号：</span>
                    <span class="title-r col">{{
                        alarmFormData?.deviceSn
                    }}</span>
                </div>
                <div class="py-1">
                    <span class="title-l title-content">发生时间：</span>
                    <span class="title-r col">{{
                        alarmFormData?.alarmTime
                    }}</span>
                </div>
                <div class="py-1" v-if="alarmFormData?.alarmStatus !== 0">
                    <span class="title-l title-content">处理操作：</span>
                    <span class="title-r col">{{
                        alarmFormData?.disposeAction
                            ? alarmFormData?.disposeAction == 1
                                ? '自动恢复'
                                : '忽略异常'
                            : ''
                    }}</span>
                </div>
                <div class="py-1" v-if="alarmFormData?.alarmStatus !== 0">
                    <span class="title-l title-content">处理时间：</span>
                    <span class="title-r col">{{
                        alarmFormData?.alarmStatus == 1
                            ? alarmFormData?.recoverTime
                            : alarmFormData?.disposeTime
                    }}</span>
                </div>
                <div class="py-1" v-if="alarmFormData?.alarmStatus !== 0">
                    <span class="title-l title-content">处理人：</span>
                    <span class="title-r col">{{
                        alarmFormData?.disposeRealName || '-'
                    }}</span>
                </div>
            </div>
            <div></div>
        </div>
    </el-drawer>
</template>

<script>
import { watch, toRefs, inject, reactive, ref, getCurrentInstance } from 'vue'
import {
    alarmStatusList,
    alarmLevelList,
    stationNameList,
    deviceTypeList,
} from '../../const'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import powerApi from '@/apiService/power'

import Dictionary from '@/components/table/dictionary.vue'
export default {
    components: { Dictionary },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        alarmId: {
            type: String,
            default: '',
        },
    },
    setup(props, { emit }) {
        const { visible, alarmId } = toRefs(props)
        const { proxy } = getCurrentInstance()
        const router = useRouter()
        const route = useRoute()
        const store = useStore()
        const alarmFormData = ref()
        const state = reactive({})
        const onClose = () => {
            emit('update:visible', false)
        }
        const GetDeviceAlarmDetail = async (val) => {
            let { data: data, code } = await powerApi.powerDeviceAlarmDetail({
                alarmId: val,
            })
            alarmFormData.value = data.data
        }

        // 清除告警事件
        const onCleared = async (val) => {
            let { data: data, code } =
                await apiService.PostDeviceAlarmClearAlarm({ alarmId: val })
            GetDeviceAlarmDetail(props.alarmId)
            if (data.code == 0) {
                proxy.$message.success('操作成功')
                onClose()
                emit('success')
            }
        }
        // 跳转工单详情
        const workOrderDetails = () => {
            const { stationNo } = alarmFormData.value
            router.push(`/device/deviceDetail?stationNo=${stationNo}`)
        }
        watch(
            visible,
            async (val) => {
                if (val) {
                    GetDeviceAlarmDetail(alarmId.value)
                }
            },
            { immediate: true }
        )
        return {
            onClose,
            ...toRefs(state),
            GetDeviceAlarmDetail,
            alarmFormData,
            alarmStatusList,
            alarmLevelList,
            stationNameList,
            deviceTypeList,
            onCleared,
            workOrderDetails,
        }
    },
}
</script>

<style scoped lang="less">
.title-content {
    height: 22px;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    // color: #222222;
    color: theme('colors.secondar-text');
    line-height: 22px;
}

.col {
    height: 22px;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #222222;
    line-height: 22px;
}

.alarm-stauts {
    :deep(.text) {
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #595959;
    }
}

.alarm-level {
    :deep(.text-label) {
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #222222;
    }
}

.alarm-type {
    :deep(.text-label) {
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #222222;
    }
}
</style>
