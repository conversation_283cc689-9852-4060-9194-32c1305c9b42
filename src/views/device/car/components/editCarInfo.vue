<template>
    <el-drawer
        :model-value="visible"
        size="486"
        @close="onClose"
        :maskClosable="true"
        :show-close="false"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{
                        isAddNew ? $t('AddStation') : $t('bianjizhandianxinxi')
                    }}</span>
                </div>
                <div class="flex gap-x-3">
                    <el-button plain round @click="onClose">
                        <span>{{ $t('Cancle') }}</span>
                    </el-button>
                    <el-popconfirm
                        :title="$t('biangengriqi')"
                        @confirm="onSave"
                        confirm-button-text="$t('common_shi')"
                        cancel-button-text="$t('common_fou')"
                        width="236"
                        v-if="showConfirm"
                    >
                        <template #reference>
                            <el-button plain round type="primary">
                                <span>{{ $t('Save') }}</span>
                            </el-button>
                        </template>
                    </el-popconfirm>
                    <el-button
                        v-else
                        plain
                        round
                        @click="onSave"
                        :loading="addLoading"
                        type="primary"
                        >{{ $t('Save') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div class="add-site">
            <a-form
                ref="formRef"
                :model="formState"
                :rules="rules"
                autocomplete="off"
                :hideRequiredMark="true"
                :label-col="labelCol"
                labelAlign="left"
            >
                <div>
                    <a-form-item
                        :label="$t('station_bangdingqiye')"
                        name="orgId"
                    >
                        <a-tree-select
                            v-model:value="formState.orgId"
                            style="width: 100%"
                            :replaceFields="{
                                title: 'name',
                                children: 'children',
                                key: 'id',
                                value: 'id',
                            }"
                            :tree-data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            tree-default-expand-all
                            :disabled="inputDisabled"
                        >
                        </a-tree-select>
                    </a-form-item>
                    <a-form-item
                        :label="$t('chezhanleixing')"
                        name="vehicleStationType"
                    >
                        <a-select
                            v-model:value="formState.vehicleStationType"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="vehicleStationTypes"
                        />
                    </a-form-item>

                    <a-form-item
                        :label="$t('chezhanmingcheng')"
                        name="stationName"
                    >
                        <a-input
                            v-model:value="formState.stationName"
                            :placeholder="$t('placeholder_qingshuru')"
                            :disabled="inputDisabled"
                            maxLength="20"
                        />
                    </a-form-item>
                    <!-- ⬇️ -->
                    <a-form-item :label="$t('chezhandizhi')" name="address">
                        <div
                            class="flex justify-between items-center leading-8 h-8 border border-d9 px-2.5 cursor-pointer address-input rounded"
                            :title="formState.address"
                            @click="mapShow"
                        >
                            <div
                                class="flex-1 overflow w-0 address-text"
                                :style="{
                                    color: formState.address
                                        ? 'var(--input-color)'
                                        : 'var(--placeholder)',
                                }"
                            >
                                {{
                                    formState.address ||
                                    $t('placeholder_qingxuanze')
                                }}
                            </div>
                            <div>
                                <iconSvg name="ip" :className="'iconsIp'" />
                            </div>
                        </div>
                    </a-form-item>

                    <a-form-item :label="$t('chezhanzuobiao')">
                        <a-input
                            disabled
                            :value="
                                formState.longitude &&
                                $t('longitude') +
                                    formState.longitude +
                                    $t('latitude') +
                                    formState.latitude
                            "
                            :title="
                                formState.longitude &&
                                $t('longitude') +
                                    formState.longitude +
                                    $t('latitude') +
                                    formState.latitude
                            "
                        >
                        </a-input>
                    </a-form-item>

                    <!-- ⬆️ -->

                    <a-form-item
                        :label="$t('station_touyunzhuangtai')"
                        name="operationStatus"
                        v-if="!inputDisabled"
                    >
                        <a-select
                            v-model:value="formState.operationStatus"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="operationStatuss"
                        />
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_touyunriqi')"
                        v-if="!inputDisabled"
                    >
                        <a-date-picker
                            class="w-full"
                            v-model:value="formState.startDate"
                            valueFormat="YYYY-MM-DD"
                            showArrow
                        />
                    </a-form-item>

                    <a-form-item
                        :label="$t('station_weihurenyuan')"
                        name="maintenanceStaffId"
                        v-if="!inputDisabled && isShangshan"
                    >
                        <a-select
                            v-model:value="formState.maintenanceStaffId"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="maintenanceStaffs"
                        />
                    </a-form-item>
                    <a-form-item
                        :label="$t('station_zhandiantupian')"
                        v-show="!isAddNew"
                    >
                        <div style="width: 160px; height: 92px">
                            <Upload
                                :data="{ scene: 'stationPic' }"
                                :className="'orgLogo'"
                                @success="success"
                                v-model:file-list="formState.stationPic"
                                @delImgFile="delImgFile"
                            />
                        </div>
                    </a-form-item>
                    <div class="h-1"></div>
                </div>
                <div v-if="isAddNew">
                    <div class="flex justify-between items-center mb-3">
                        <div class="text-secondar-text dark:text-60-dark">
                            {{ $t('station_shebeixinxi') }}:
                        </div>
                        <el-button
                            plain
                            round
                            size="small"
                            type="primary"
                            @click="addNewDevice"
                            >{{ $t('add_device') }}</el-button
                        >
                    </div>
                    <div
                        class="bg-background dark:bg-ff-dark px-3 py-4 mb-3 rounded"
                        v-for="(item, index) in formState.bindDeviceList"
                        :key="index"
                    >
                        <a-form-item
                            :label="$t('device_shebeimingcheng')"
                            name="deviceName"
                        >
                            <a-input
                                v-model:value="item.deviceName"
                                :placeholder="$t('placeholder_qingshuru')"
                            >
                            </a-input>
                        </a-form-item>
                        <a-form-item :label="$t('Device No')" name="deviceSn">
                            <a-input
                                v-model:value="item.deviceSn"
                                :disabled="isAddNew ? false : true"
                                :placeholder="$t('placeholder_qingshuru')"
                            >
                            </a-input>
                        </a-form-item>
                        <a-form-item :label="$t('Vehicle type')">
                            <a-select
                                v-model:value="item.vehicleType"
                                :placeholder="$t('placeholder_qingxuanze')"
                                :options="vehicleTypes"
                            />
                        </a-form-item>
                        <a-form-item :label="$t('device_dianxinxinghao')">
                            <a-select
                                v-model:value="item.productModel"
                                :placeholder="$t('placeholder_qingxuanze')"
                                :options="productModels"
                            />
                        </a-form-item>
                        <div
                            v-if="isAddNew"
                            class="flex justify-center items-center gap-x-3 text-xs"
                        >
                            <el-button
                                round
                                size="small"
                                linear
                                @click="deleteItem(index)"
                                >{{ $t('Delete') }}</el-button
                            >
                            <el-button
                                plain
                                round
                                size="small"
                                type="primary"
                                @click="copyItem(index)"
                                >{{ $t('Copy') }}</el-button
                            >
                        </div>
                    </div>
                </div>
            </a-form>
        </div>
    </el-drawer>
    <a-modal
        v-model:visible="mapVisible"
        :title="$t('ditu')"
        :footer="null"
        :destroyOnClose="true"
        :width="850"
        wrapClassName="modal-box"
    >
        <gaodeMap @queryClick="queryClick" />
    </a-modal>
</template>

<script setup>
import { reactive, ref, watch, computed, onMounted, nextTick, toRaw } from 'vue'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import { roundNumFun, operationStatuss } from '@/views/device/const.js'
import gaodeMap from '@/views/role/components/map.vue'
import Upload from '@/views/role/components/Upload.vue'
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const labelCol = computed(() => {
    let res =
        locale.value == 'zh'
            ? {
                  span: 4,
              }
            : locale.value == 'en'
            ? {
                  span: 9,
              }
            : {
                  span: 10,
              }
    return res
})
const icon = require('@/assets/login/icon.png')
const route = useRoute(),
    router = useRouter()
const props = defineProps({
    visible: Boolean,
    info: {
        type: Object,
        default: () => {},
    },
    isAddNew: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['onClose', 'update'])

const oldCreateTime = computed(() => props.info?.createTime || '')
const showConfirm = computed(() => {
    return (
        !inputDisabled.value &&
        !props.isAddNew &&
        oldCreateTime.value !== formState.startDate
    )
})
const getUserNames = async () => {
    let res = await apiService.getStaffByRole({ roleId: 3 })
    maintenanceStaffs.value = res.data.data.map((item) => ({
        label: item.name || '',
        value: item.id,
    }))
}
watch(
    () => props.visible,
    async (val) => {
        if (val) {
            store.dispatch('dictionary/getDictionary', 'vehicleStationType')
            store.dispatch('dictionary/getDictionary', 'vehicleType')
            store.dispatch('dictionary/getDictionary', 'vehicleBatteryModel')
            await getUserNames()
            await getTreeData()
            await PotsQueryAreaList()
        }
        if (val && !props.isAddNew) {
            formState.orgId = props.info.customerId
            formState.stationId = props.info.id
            formState.stationName = props.info.stationName
            formState.province = props.info.province
            formState.city = props.info.city
            formState.district = props.info.district
            formState.address = props.info.address
            formState.longitude = props.info.longitude
            formState.latitude = props.info.latitude
            formState.startDate = props.info.createTime
            formState.operationStatus = props.info.operationStatus || 0
            formState.vehicleStationType =
                props.info.vehicleStationType.value || undefined
            formState.bindDeviceList = props.info.devices.map((item) => {
                return {
                    deviceName: item.deviceName,
                    deviceSn: item.deviceSn,
                    vehicleType: item.vehicleType.value,
                    productModel: item.productModel,
                }
            })
            formState.maintenanceStaffId = props.info.maintenanceStaffId
            // formState.transformerCapacity = props.info.transformerCapacity
            // formState.stationLoad = props.info.stationLoad
            // formState.orgId = inputDisabled.value ? props.info.orgId : 0
            nextTick(() => {
                formState.stationPic = [
                    {
                        name: 'stationPic',
                        url: props.info.stationPic
                            ? props.info.stationPic
                            : void 0,
                    },
                ]
            })
        }
    },
    { immediate: true }
)

const onClose = () => {
    formRef.value?.clearValidate()
    formRef.value?.resetFields()
    formState.bindDeviceList = [
        {
            deviceName: '',
            deviceSn: '',
            vehicleType: undefined,
            productModel: undefined,
        },
    ]
    emit('onClose')
}
const addLoading = ref(false)
const hasUniqueIds = (arr, type) => {
    const seenIds = new Set()
    for (const item of arr) {
        if (seenIds.has(item[type])) {
            return false // 发现重复的 deviceSn
        }
        seenIds.add(item[type])
    }
    return true // 所有 deviceSn 都是唯一的
}
const onSave = async () => {
    //
    addLoading.value = true
    if (props.isAddNew) {
        // 先进行表单验证
        let hasRepeatDeviceName = hasUniqueIds(
            formState.bindDeviceList,
            'deviceName'
        )
        let hasRepeatDeviceSn = hasUniqueIds(
            formState.bindDeviceList,
            'deviceSn'
        )
        formRef.value
            .validate()
            .then(async () => {
                //
                if (formState.bindDeviceList.some((item) => !item.deviceName)) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips05'))
                } else if (
                    formState.bindDeviceList.some((item) => !item.deviceSn)
                ) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips02'))
                } else if (
                    formState.bindDeviceList.some((item) => !item.vehicleType)
                ) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips06'))
                } else if (
                    formState.bindDeviceList.some((item) => !item.productModel)
                ) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips07'))
                } else if (!hasRepeatDeviceName) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips08'))
                } else if (!hasRepeatDeviceSn) {
                    addLoading.value = false
                    message.error(t('tianjiashebei_tips03'))
                } else {
                    const params = {
                        ...formState,
                    }
                    try {
                        console.log('[ params ] >', params)
                        let res = await carApi.bindStationVehicle(params)
                        if (res.data.code === 0) {
                            message.success(t('caozuochenggong'))
                            // const currentRoute = router.currentRoute.value
                            // }

                            onClose()
                            window.location.reload()
                        }
                        if (res.data.code != 0) {
                            // message.error(res.data.msg)
                        }
                        // addLoading.value = false
                    } catch (error) {
                        addLoading.value = false
                    }
                }
            })
            .catch((error) => {
                addLoading.value = false
            })
        // 判断devices中是否有未填写设备编号的，提示
    } else {
        const params = {
            ...formState,
            stationId: formState.stationId,
            stationPic: formState.stationPic[0]?.url || '',
            startDate: !inputDisabled.value
                ? dayjs(formState.startDate).format('YYYY-MM-DD')
                : undefined,
            bindOrgId: formState.orgId,
            bindDeviceList: undefined,
        }
        try {
            console.log('[ params ] >', params)
            let res = await carApi.updateStationInfoVehicle(params)
            if (res.data.code === 0) {
                message.success(t('caozuochenggong'))
                onClose()
                emit('update')
            }

            addLoading.value = false
        } catch (error) {
            addLoading.value = false
        }
    }

    addLoading.value = false
}

const store = useStore()
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
})
const isShangshan = computed(() => {
    return getCompanyInfo?.value?.orgId == '1726907974555729921'
})
const inputDisabled = computed(() => {
    // 新增，可以输入,编辑不可以输入,但如果编辑的时候，是操作员，那么可以输入

    return props.isAddNew ? false : isOperator.value ? false : true
})
const rules = {
    orgId: [
        {
            required: true,
            message: t('placeholder_qingxuanzebangdingqiye'),
            trigger: 'blur',
        },
    ],
    // stationId: [{ required: true, message: '请输入站点编号', trigger: 'blur' }],
    vehicleStationType: [
        {
            required: true,
            message: t('placeholder_qingxuanze') + t('chezhanleixing'),
        },
    ],
    stationName: [
        {
            required: true,
            message: t('placeholder_qingshuru') + t('chezhanmingcheng'),
            trigger: 'blur',
        },
    ],

    address: [
        {
            required: true,
            message: t('placeholder_qingxuanze') + t('chezhandizhi'),
            trigger: 'change',
        },
    ],
}

const mapVisible = ref(false)

const formRef = ref(null)
const formState = reactive({
    orgId: undefined,
    vehicleStationType: undefined,
    stationId: void 0, // 编辑时用
    stationName: void 0,
    province: void 0,
    city: void 0,
    district: void 0,
    address: void 0,
    latitude: void 0,
    longitude: void 0,

    bindDeviceList: [
        {
            deviceName: '',
            deviceSn: '',
            vehicleType: undefined,
            productModel: undefined,
        },
    ],
    stationPic: undefined, // 编辑时用
    // transformerCapacity: undefined,
    // stationLoad: undefined,
    maintenanceStaffId: undefined,
})

const queryAreaListData = ref([])

const PotsQueryAreaList = async (val) => {
    let {
        data: { data },
    } = await apiService.PotsQueryAreaList()
    queryAreaListData.value =
        data?.map((item) => {
            return { label: item.areaName, value: item.id }
        }) || []
}

const queryClick = (data) => {
    mapVisible.value = false
    formState.address = data.label
    formState.province = data.pname
    formState.city = data.cityname
    formState.district = data.adname
    formState.longitude = data.location.lng
    formState.latitude = data.location.lat
}

// changeQuantity(1, {
//     label: '1台',
//     value: 1,
//     textValue: 215,
// })

const mapShow = () => {
    mapVisible.value = true
}

const submit = () => {
    return formRef.value.validate()
}

const clearValidate = () => {
    formRef.value.clearValidate()
}

const resetFields = () => {
    formRef.value.resetFields()
}
const treeData = ref([])
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const getTreeData = async () => {
    const {
        data: { data, code },
    } = await apiService.getDeviceTree({
        supplierId: getCompanyInfo?.value?.orgId,
        businessType: 'vehicle_battery',
    })
    if (code === 0) {
        const tree = [
            {
                name: getCompanyInfo?.value?.orgName,
                id: getCompanyInfo?.value?.orgId,
                children: data || [],
            },
        ]
        treeData.value = tree
    }
}
const success = (info) => {
    const {
        scene,
        file: {
            response: {
                data: { fileVisitUrl },
            },
        },
    } = info
    // currentOrgData.loginBaner = fileVisitUrl ? fileVisitUrl : void 0
    formState.stationPic = [
        { name: 'stationPic', url: fileVisitUrl ? fileVisitUrl : void 0 },
    ]
}
const delImgFile = () => {
    formState.stationPic = []
}

defineExpose({ submit, clearValidate, resetFields, formState })
const addNewDevice = () => {
    formState.bindDeviceList.push({
        deviceName: '',
        deviceSn: '',
        vehicleType: undefined,
        productModel: undefined,
    })
}
const deleteItem = (index) => {
    //
    if (formState.bindDeviceList.length <= 1) {
        message.warning(t('tianjiashebei_tips09'))
    } else {
        formState.bindDeviceList.splice(index, 1)
    }
}
const copyItem = (index) => {
    //
    formState.bindDeviceList.splice(index + 1, 0, {
        ...formState.bindDeviceList[index],
    })
}
const vehicleStationTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleStationType || []
)
const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const productModels = computed(
    () => store.state.dictionary.dictionaries.vehicleBatteryModel || []
)
const maintenanceStaffs = ref([])

onMounted(async () => {})
</script>

<style scoped lang="less">
.add-site {
    :deep(.iconsIp) {
        width: 16px;
        height: 16px;
        margin-right: 0;
        cursor: pointer;
    }

    :deep(.ant-form) {
        .ant-form-item-has-success {
            // margin-bottom: 24px !important;
        }

        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }

        .ant-form-item {
            font-size: 14px;
            margin-bottom: 12px;

            .ant-form-item-label {
                width: 72px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: rgba(34, 34, 34, 0.65);

                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
            }

            .ant-form-item-control-input {
                min-height: 32px;

                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 10px;
                        font-size: 14px;
                    }

                    .ant-input-affix-wrapper {
                        padding: 4px 10px;

                        input {
                            padding: 0;
                        }

                        .ant-input-suffix {
                            margin-left: 4px;
                        }

                        &::before {
                            height: 22px;
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }

                        &:hover {
                            border-color: var(--themeColor);
                        }
                    }

                    .ant-input-affix-wrapper-focused {
                        border-color: var(--themeColor);
                        box-shadow: none;
                    }

                    .ant-input {
                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }

                    .ant-select-multiple {
                        font-size: 14px;

                        .ant-select-selector {
                            padding: 1px 4px;
                            font-size: 14px;

                            .ant-select-selection-item {
                                height: 24px;
                                margin-top: 2px;
                                margin-bottom: 2px;
                                line-height: 22px;
                                margin-inline-end: 4px;
                                padding-inline-start: 8px;
                                padding-inline-end: 8px;
                            }

                            &::after {
                                line-height: 24px;
                            }
                        }

                        .ant-select-selection-search-input,
                        .ant-select-multiple
                            .ant-select-selection-search-mirror {
                            height: 24px;
                        }
                    }

                    .ant-select {
                        font-size: 14px;

                        &:not(.ant-select-disabled) {
                            &:hover {
                                .ant-select-selector {
                                    border-color: var(--themeColor);
                                }
                            }
                        }

                        .ant-select-selector {
                            height: 32px;
                            padding: 0 10px;

                            .ant-select-selection-search-input {
                                height: 30px;
                            }

                            .ant-select-selection-item {
                                line-height: 30px;
                                padding-right: 18px;
                            }
                        }

                        .ant-select-arrow {
                            right: 10px;
                            width: 12px;
                            height: 12px;
                            margin-top: -6px;
                            font-size: 12px;
                        }
                    }

                    .ant-select-single,
                    .ant-select-show-arrow {
                        .ant-select-selection-placeholder {
                            padding-right: 18px;
                            line-height: 30px;
                        }
                    }
                }
            }
        }

        .ant-select-focused {
            .ant-select-selector {
                border-color: var(--themeColor);
                box-shadow: none !important;
            }
        }
    }
}

:deep(.orgLogo) {
    .ant-upload {
        margin: 0;
        width: 94px;
        height: 92px;
        padding: 0px;
        border-style: solid;
        border-color: #d9d9d9;

        img {
            max-height: 90px;
        }
    }
}

:deep(.ant-btn-primary) {
    background: var(--themeColor);
    border-color: var(--themeColor);
    outline-color: var(--themeColor);
}

:deep(.ant-btn-primary) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);

    &:hover {
        color: #fff;
        border-color: var(--themeColor);
    }
}

.address-input {
    &:hover {
        border-color: var(--themeColor);
    }
}

:deep(.ant-calendar-picker .ant-calendar-picker-clear) {
    display: block;
}

:deep(.ant-popover-inner-content) {
    width: 240px;
}
:deep(.iconsIp) {
    color: var(--input-color);
}
</style>

<style lang="less">
.modal-box {
    .ant-modal-header {
        padding: 16px 24px;
        border-radius: 8px 8px 0 0;

        .ant-modal-title {
            font-size: 16px;
        }
    }

    .ant-modal-close-x {
        width: 56px;
        height: 56px;
        font-size: 16px;
        line-height: 56px;
    }

    .ant-modal-body {
        // padding: 10px;
    }
}
</style>
