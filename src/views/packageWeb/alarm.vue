<template>
    <div class="alarm-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h2>告警信息</h2>
        </div>

        <!-- Tab切换 -->
        <div class="tabs-container">
            <el-tabs
                v-model="activeKey"
                @tab-change="onChange"
                class="alarm-tabs"
            >
                <el-tab-pane
                    v-for="item in tabOptions"
                    :key="item.id"
                    :label="item.label"
                    :name="item.id"
                >
                    <!-- 列表内容 -->
                    <div class="list-container" ref="listContainer">
                        <div
                            v-for="item in list"
                            :key="item.id"
                            class="list-item"
                        >
                            <AlarmItem :data="item" @refresh="refresh" />
                        </div>

                        <!-- 加载更多指示器 -->
                        <div class="load-more" v-if="list.length > 0">
                            <el-button
                                v-if="loadMoreStatus === 'more'"
                                @click="loadMore"
                                :loading="isLoadingMore"
                                type="primary"
                                text
                            >
                                {{ isLoadingMore ? '加载中...' : '加载更多' }}
                            </el-button>
                            <span
                                v-else-if="loadMoreStatus === 'noMore'"
                                class="no-more"
                            >
                                没有更多数据了
                            </span>
                        </div>

                        <!-- 空状态 -->
                        <div
                            class="empty"
                            v-if="list.length === 0 && !isLoading"
                        >
                            <!-- <img src="@/static/empty.png" alt="暂无数据" /> -->
                            <div class="text">
                                {{
                                    activeKey === 'A'
                                        ? '当前系统安全运行，暂无任何异常'
                                        : '没有历史异常'
                                }}
                            </div>
                        </div>

                        <!-- 加载中状态 -->
                        <div
                            class="loading"
                            v-if="isLoading && list.length === 0"
                        >
                            <el-icon class="is-loading">
                                <Loading />
                            </el-icon>
                            <span>加载中...</span>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import AlarmItem from './alarmItem.vue'
import powerApi from '@/apiService/power'

const route = useRoute()

// 响应式数据
const activeKey = ref('A')
const list = ref([])
const loadMoreStatus = ref('noMore')
const isLoading = ref(false)
const isLoadingMore = ref(false)
const listContainer = ref()

// Tab选项配置
const tabOptions = ref([
    {
        label: '当前异常',
        id: 'A',
    },
    {
        label: '历史异常',
        id: 'B',
    },
])

// 告警类型映射
const alarmTypes = {
    A: 'current', // 当前异常
    B: 'history', // 历史异常
}

// 分页配置
const config = reactive({
    current: 1,
    size: 10,
    total: 0,
})

// 页面信息
const pageInfo = ref({
    sn: route.query.sn || '',
})

// Tab切换事件
const onChange = (val) => {
    console.log('切换Tab:', val)
    activeKey.value = val
    loadingRefresh(true)
}

// 获取告警数据
const getActivePage = async () => {
    const stage = alarmTypes[activeKey.value]
    return powerApi.powerDeviceAlarmPage({
        ...config,
        stage: stage,
        sn: pageInfo.value?.sn,
    })
}

// 获取列表数据
const listPage = async (isRefresh = false) => {
    if (isLoading.value && !isRefresh) {
        return
    }

    // 设置加载状态
    if (isRefresh) {
        isLoading.value = true
    } else {
        isLoadingMore.value = true
    }

    try {
        const {
            data: {
                data: { records, total },
            },
        } = await getActivePage()

        if (isRefresh) {
            // 刷新数据，替换原有数据
            list.value = records || []
        } else {
            // 加载更多，追加数据
            list.value = list.value.concat(records || [])
        }

        config.total = total

        // 判断是否还有更多数据
        if (config.current * config.size >= total) {
            loadMoreStatus.value = 'noMore'
        } else {
            loadMoreStatus.value = 'more'
        }
    } catch (error) {
        console.error('获取告警数据失败:', error)
        ElMessage.error('数据加载失败')
    } finally {
        isLoading.value = false
        isLoadingMore.value = false
    }
}

// 加载更多
const loadMore = () => {
    if (loadMoreStatus.value === 'more' && !isLoadingMore.value) {
        config.current = config.current + 1
        listPage(false)
    }
}

// 刷新数据
const loadingRefresh = (isRefresh = false) => {
    config.current = 1
    loadMoreStatus.value = 'more'

    // 滚动到顶部
    if (listContainer.value) {
        listContainer.value.scrollTop = 0
    }

    listPage(isRefresh)
}

// 子组件刷新回调
const refresh = (shouldRefresh = true) => {
    if (shouldRefresh) {
        loadingRefresh(true)
    }
}

// 页面挂载时
onMounted(() => {
    console.log('alarm页面加载')
    // 初始化数据
    loadingRefresh(true)
})
</script>

<style lang="less" scoped>
.alarm-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;

    .page-header {
        margin-bottom: 20px;

        h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
    }

    .tabs-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .alarm-tabs {
            :deep(.el-tabs__header) {
                margin: 0;
                background: #fafafa;
                border-bottom: 1px solid #e4e7ed;
            }

            :deep(.el-tabs__nav-wrap) {
                padding: 0 20px;
            }

            :deep(.el-tabs__item) {
                font-size: 16px;
                font-weight: 500;
                color: #666;

                &.is-active {
                    color: #3edacd;
                    font-weight: 600;
                }
            }

            :deep(.el-tabs__active-bar) {
                background-color: #3edacd;
            }
        }

        .list-container {
            min-height: 400px;
            overflow-y: auto;
            padding: 20px;

            .list-item {
                margin-bottom: 12px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .load-more {
                text-align: center;
                padding: 20px 0;

                .no-more {
                    color: #999;
                    font-size: 14px;
                }
            }

            .empty {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 60px 20px;
                color: #999;

                img {
                    width: 120px;
                    height: 120px;
                    margin-bottom: 16px;
                    opacity: 0.6;
                }

                .text {
                    font-size: 16px;
                    line-height: 1.5;
                    text-align: center;
                }
            }

            .loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 60px 20px;
                color: #666;

                .el-icon {
                    font-size: 24px;
                    margin-bottom: 12px;
                }

                span {
                    font-size: 14px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .alarm-container {
        padding: 10px;

        .page-header h2 {
            font-size: 20px;
        }

        .tabs-container .list-container {
            padding: 15px;
        }
    }
}
</style>