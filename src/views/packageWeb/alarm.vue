<template>
    <div class="alarm">
        <view class="tabs">
            <view
                v-for="(item, index) in tabOptions"
                :key="item.id"
                class="tab"
                :class="[activeKey == item.id ? 'selected' : '']"
                @click="onChange(item.id, index)"
                :id="'tab' + item.id"
                >{{ item.label }}</view
            >
        </view>
        <scroll-view
            scroll-y
            @scrolltolower="scrolltolower"
            class="scorll"
            :class="{ 'scorll-operator': true }"
        >
            <view v-for="item in list" :key="item.id" class="list">
                <AlarmItem :data="item" @refresh="refresh" />
            </view>
            <uni-load-more
                :status="loadMoreStatus"
                v-if="list.length > 0"
            ></uni-load-more>
            <view class="empty" v-if="list.length == 0">
                <image src="@/static/empty.png" />
                <view class="text">{{
                    activeKey == 'A'
                        ? '当前系统安全运行，暂无任何异常'
                        : '没有历史异常'
                }}</view>
            </view>
        </scroll-view>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { onPullDownRefresh, onLoad } from '@dcloudio/uni-app'
import AlarmItem from './alarmItem.vue'
import powerApi from '@/apiService/power'

// 响应式数据
const activeKey = ref('A')
const list = ref([])
const loadMoreStatus = ref('noMore')
const isLoading = ref(false)

// Tab选项配置
const tabOptions = ref([
    {
        label: '当前异常',
        id: 'A',
    },
    {
        label: '历史异常',
        id: 'B',
    },
])

// 告警类型映射
const alarmTypes = {
    A: 'current', // 当前异常
    B: 'history', // 历史异常
}

// 分页配置
const config = reactive({
    current: 1,
    size: 10,
    total: 0,
})

// Tab切换事件
const onChange = (val, ind) => {
    console.log('切换Tab:', val, ind)
    activeKey.value = val
    loadingRefresh(true)
}

// 获取告警数据
const getActivePage = async () => {
    const stage = alarmTypes[activeKey.value]
    return powerApi.powerDeviceAlarmPage({
        ...config,
        stage: stage,
        sn: pageInfo.value?.sn,
    })
}

// 获取列表数据
const listPage = async (boolean = false) => {
    if (isLoading.value && !boolean) {
        return
    }

    uni.showLoading({
        title: '加载中',
    })

    try {
        const {
            data: {
                data: { records, total },
            },
        } = await getActivePage()

        uni.stopPullDownRefresh()
        uni.hideLoading()

        if (boolean) {
            // 刷新数据，替换原有数据
            list.value = records || []
        } else {
            // 加载更多，追加数据
            list.value = list.value.concat(records || [])
        }

        config.total = total

        // 判断是否还有更多数据
        if (config.current * config.size >= total) {
            isLoading.value = true
            loadMoreStatus.value = 'noMore'
        } else {
            isLoading.value = false
            loadMoreStatus.value = 'more'
        }
    } catch (error) {
        console.error('获取告警数据失败:', error)
        uni.stopPullDownRefresh()
        uni.hideLoading()
        uni.showToast({
            title: '数据加载失败',
            icon: 'none',
        })
    }
}

// 上拉加载更多
const scrolltolower = () => {
    if (!isLoading.value) {
        console.log('触发上拉加载更多')
        config.current = config.current + 1
        listPage()
    }
}

// 刷新数据
const loadingRefresh = (boolean = false) => {
    config.current = 1
    isLoading.value = false
    loadMoreStatus.value = 'more'

    // 滚动到顶部
    uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
    })

    listPage(boolean)
}

// 下拉刷新
onPullDownRefresh(() => {
    console.log('触发下拉刷新')
    loadingRefresh(true)
})

// 子组件刷新回调
const refresh = (shouldRefresh = true) => {
    if (shouldRefresh) {
        loadingRefresh(true)
    }
}

// 页面显示时
onShow(() => {
    console.log('alarm页面显示')
    loadingRefresh(true)
})
const pageInfo = ref()
// 页面加载时
onLoad((options) => {
    console.log('alarm页面加载', options)
    // 可以根据传入的参数设置默认tab
    pageInfo.value = options
})
</script>

<style lang="scss" scoped>
.alarm {
    position: relative;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: rgba(30, 49, 43, 0.04);

    .tabs {
        padding: 24rpx;
        white-space: nowrap;
        display: flex;
        line-height: 40rpx;
        justify-content: flex-start;
        gap: 48rpx;

        .tab {
            padding-bottom: 16rpx;
            color: $uni-secondary-color;
            text-align: center;
            position: relative;

            &.selected {
                color: $uni-main-color;
                font-weight: bold;

                &::after {
                    content: '';
                    height: 8rpx;
                    width: 48rpx;
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: $uni-primary;
                    border-radius: 4rpx;
                }
            }
        }
    }

    .scorll {
        flex: 1;
        //#ifdef MP-DINGTALK
        height: calc(100vh - 180rpx);
        //#endif
        //#ifdef MP-WEIXIN
        height: calc(100vh - 184rpx);
        //#endif
    }

    .scorll-operator {
        height: calc(100vh - 96rpx);
    }

    .list {
        margin-top: 24rpx;

        &:first-child {
            margin-top: 0;
        }
    }

    .empty {
        position: absolute;
        top: 20%;
        width: 100%;
        text-align: center;

        image {
            width: 200rpx;
            height: 200rpx;
            margin-bottom: 24rpx;
        }

        .text {
            height: 44rpx;
            font-size: 28rpx;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            color: #000000;
            line-height: 44rpx;
        }
    }
}
</style>