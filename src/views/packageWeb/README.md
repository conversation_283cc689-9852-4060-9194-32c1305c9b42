# Distributed 组件使用说明

## 概述

本目录包含多个图表组件，用于显示电池相关数据：

- `distributed.vue` - 电芯电压热力分布图
- `tempDistributed.vue` - 电芯温度热力分布图
- `gauge.vue` - 电池电量仪表盘组件（支持水球图和仪表盘两种模式）
- `newBar.vue` - 充放电量柱状图组件（支持电量/时长视图切换和时间段筛选）

这些组件已经完善，支持在 **H5** 和 **微信小程序** 中正常渲染。

## 功能特性

### 跨平台支持
- ✅ H5 浏览器环境
- ✅ 微信小程序环境
- 🔄 自动适配不同平台的 ECharts 引入方式

### 图表功能
- 热力图可视化
- 自动计算数据范围
- 响应式布局
- 交互式提示框
- 数据更新支持

## 技术实现

### 架构设计
- 使用 `EchartWrapper.vue` 作为统一的图表包装器
- 通过条件编译处理不同平台的差异
- 统一的事件回调机制

### H5 环境
- 使用 `import * as echarts from "echarts"` 导入
- 通过 `document.getElementById` 获取 DOM 元素
- 直接使用 `echarts.init()` 初始化

### 微信小程序环境
- 使用 `require` 方式导入 echarts
- 使用 `l-echart` 组件包装
- 通过 `@finished` 事件处理初始化

## 使用方法

### 基本使用

```vue
<template>
  <view class="chart-wrapper">
    <!-- 电压分布图 -->
    <view class="voltage-chart">
      <distributed ref="voltageChart" />
    </view>

    <!-- 温度分布图 -->
    <view class="temp-chart">
      <temp-distributed ref="tempChart" />
    </view>

    <!-- 电量仪表盘 -->
    <view class="gauge-chart">
      <gauge ref="gaugeChart" />
    </view>

    <!-- 充放电量柱状图 -->
    <view class="bar-chart">
      <new-bar ref="barChart" width="100%" :height="190" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import distributed from './distributed.vue'
import tempDistributed from './tempDistributed.vue'
import gauge from './gauge.vue'
import newBar from './newBar.vue'

const voltageChart = ref()
const tempChart = ref()
const gaugeChart = ref()
const barChart = ref()

// 更新图表数据
const updateCharts = () => {
  const voltageData = [3.1, 3.2, 3.0, 3.3, 3.1, 3.2, 3.4, 3.0, 3.2, 3.1]
  const tempData = [25, 26, 24, 27, 25, 26, 28, 24, 26, 25]

  voltageChart.value?.updateChart(voltageData)
  tempChart.value?.updateChart(tempData)
  // gauge组件会自动显示内置的电量数据
  // newBar组件会自动获取和显示充放电数据
}

// 获取当前柱状图视图
const getCurrentBarView = () => {
  return barChart.value?.getCurrentView()
}

// 刷新柱状图数据
const refreshBarData = () => {
  barChart.value?.refreshData()
}
</script>
```

### 样式设置

```scss
.chart-wrapper {
  .voltage-chart {
    width: 100%;
    height: 326rpx;
    margin-bottom: 20rpx;
  }

  .temp-chart {
    width: 100%;
    height: 226rpx;
    margin-bottom: 20rpx;
  }

  .gauge-chart {
    width: 100%;
    height: 300rpx;
  }

  .bar-chart {
    width: 100%;
    height: 400rpx;
    margin-top: 20rpx;
  }
}
```

## NewBar 组件详细说明

### 功能特性
- **双视图切换**: 支持充放电电量和充放电时长两种视图
- **时间段筛选**: 支持近七日、近30天、近一年三种时间段
- **自动数据获取**: 切换时间段时自动调用接口获取新数据
- **响应式设计**: 支持H5和微信小程序双端适配

### 组件参数
```javascript
// Props
width: String | Number  // 图表宽度，默认 '100%'
height: String | Number // 图表高度，默认 190 (px)
```

### 视图切换功能
```javascript
// 视图类型
'capacity'  // 充放电电量视图 (kWh)
'duration'  // 充放电时长视图 (h)

// 切换方法
barChart.value?.switchView('capacity')  // 切换到电量视图
barChart.value?.switchView('duration')  // 切换到时长视图
```

### 时间段配置
```javascript
// 近七日
{
  startDate: "2024-06-16",
  endDate: "2025-06-23",
  periodType: "day"
}

// 近30天
{
  startDate: "2024-05-23",
  endDate: "2025-06-23",
  periodType: "day"
}

// 近一年
{
  startMonth: "2024-06",
  endMonth: "2025-06",
  periodType: "month"
}
```

### 数据格式
```javascript
// 接口返回数据格式
[{
  chargeCap: 85.5,      // 充电电量 (kWh)
  chargeDur: 6.2,       // 充电时长 (h)
  date: "2025-05-24",   // 日期
  dischargeCap: 72.3,   // 放电电量 (kWh)
  dischargeDur: 4.8     // 放电时长 (h)
}]
```

### 暴露的方法
```javascript
// 刷新数据
barChart.value?.refreshData()

// 切换视图
barChart.value?.switchView('capacity')

// 获取当前视图
const currentView = barChart.value?.getCurrentView()

// 获取当前时间段
const currentPeriod = barChart.value?.getCurrentPeriod()
```

## Gauge 组件特殊说明

### 双模式支持
- **H5环境**: 支持水球图(liquidfill) + 仪表盘的组合显示
- **微信小程序**: 自动降级为仪表盘模式（更稳定）

### 自动适配机制
```javascript
// H5环境 - 完整功能
- 水球图显示电量百分比
- 仪表盘显示进度环
- 动态颜色变化

// 微信小程序环境 - 仪表盘模式
- 仪表盘指针显示电量
- 刻度和标签显示
- 数字显示电量百分比
```

## 组件 API

### Props
目前组件不接受外部 props，数据通过内部模拟生成。

### Methods
- `updateChart(newData: number[])` - 更新图表数据
- `getChartInstance()` - 获取 ECharts 实例

### Events
- `@finished` - 微信小程序环境下图表初始化完成事件

## 数据格式

### 电压数据
```javascript
// 20个电芯的电压值（单位：V）
const voltageData = [
  3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4, 
  3.2, 3, 5, 3, 4.2, 3, 4.1, 3, 5, 4
]
```

### 温度数据
```javascript
// 10个传感器的温度值（单位：°C）
const tempData = [3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4]
```

## 配置说明

### pages.json 配置
确保在 `pages.json` 中添加了 l-echart 组件的 easycom 配置：

```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
      "^l-echart$": "@/packageEcharts/lime-echart/components/l-echart/l-echart.vue"
    }
  }
}
```

### 依赖要求
- ECharts 5.5.1+
- lime-echart 组件（已包含在 packageEcharts 目录）
- Vue 3 Composition API

## 注意事项

1. **微信小程序环境**：确保 `packageEcharts/lime-echart/static/echarts.min.js` 文件存在
2. **数据格式**：传入的数据数组长度必须能被行数整除
3. **样式适配**：组件会自动适配容器大小，建议设置明确的高度
4. **性能优化**：大数据量时建议使用数据采样或分页显示

## 故障排除

### 常见问题

1. **微信小程序中图表不显示**
   - 检查 echarts.min.js 文件路径是否正确
   - 确认 l-echart 组件是否正确导入
   - 确保 `uni_modules/lime-echart` 目录存在且包含完整文件

2. **H5 中图表不显示**
   - 检查容器是否有明确的高度设置
   - 确认 echarts 是否正确安装和导入

3. **数据更新不生效**
   - 确保调用 `updateChart` 方法时传入正确格式的数据
   - 检查组件 ref 是否正确绑定

4. **require 模块找不到错误**
   - 确认 `packageWeb/echarts.min.js` 文件存在（已复制到本地）
   - 确认 `uni_modules/lime-echart/static/echarts.min.js` 文件存在（备用路径）
   - 检查相对路径是否正确
   - 重新编译项目，清除缓存
   - 注意：require仅支持相对路径，不支持路径别名(@/)

## 更新日志

### v1.0.0 (2024-12-21)
- ✅ 完善 H5 和微信小程序双端支持
- ✅ 优化图表配置和数据处理逻辑
- ✅ 添加错误处理和调试信息
- ✅ 统一组件 API 和使用方式
