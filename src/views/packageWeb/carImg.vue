<template>
    <div class="w-full h-full">
        <img
            class="img"
            src="@/static/car/bus.png"
            v-if="vehicleType === 'bus'"
            alt="公交车"
        />
        <img
            class="img"
            src="@/static/car/forklift.png"
            v-else-if="vehicleType === 'forklift'"
            alt="叉车"
        />
        <img
            class="img"
            src="@/static/car/loader.png"
            v-else-if="vehicleType === 'loader'"
            alt="装载机"
        />
        <img
            class="img"
            src="@/static/car/tractor.png"
            v-else-if="vehicleType === 'tractor'"
            alt="拖拉机"
        />
        <!-- 叉车和升降车目前用同一张图 -->
        <img
            class="img"
            src="@/static/car/forklift.png"
            v-else-if="vehicleType === 'liftTruck'"
            alt="升降车"
        />
        <img class="img" src="@/static/car/tractor.png" v-else alt="默认车辆" />
    </div>
</template>

<script setup>
const props = defineProps({
    vehicleType: {
        type: String,
        default: '1',
    },
})
</script>

<style lang="less" scoped>
.img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
</style>
