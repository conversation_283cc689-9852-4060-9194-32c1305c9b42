<template>
    <div id="chartDischarge">
        <!-- -->
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { pieData } from '../device/const'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import _cloneDeep from 'lodash/cloneDeep'

const { t, locale } = useI18n()
const props = defineProps({
    soc: {
        type: Number,
        default: () => 0,
    },
})

const feature = ref()
const updateChart = () => {
    let soc = props.soc ? props.soc : 0
    const option = _cloneDeep(pieData)
    const num = props.soc ? props.soc / 100 : 0
    if (soc < 10) {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#FF4D4F']
        option.series[1].itemStyle.color = '#FF4D4F'
    } else if (soc >= 10 && soc < 30) {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#FD750B']
        option.series[1].itemStyle.color = '#FD750B '
    } else {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#3EDACD']
        option.series[1].itemStyle.color = '#3EDACD'
    }
    if (!soc) {
        option.series[1].amplitude = 0
        option.series[1].waveAnimation = 0
    }
    option.series[1].data = [num, num]
    option.series[1].label.formatter = `${soc}%`
    option.series[1].label.fontSize = `20px`
    option.series[0].data[0].detail.formatter = t('station_shengyudianliang')
    let myChart
    if (myChart != null && myChart != '' && myChart != undefined) {
        myChart.dispose()
    }
    myChart = echarts.init(document.getElementById('chartDischarge'))
    myChart.setOption(option)
}
watch(
    () => props.soc,
    (val) => {
        if (val && val > 0) updateChart()
    }
)
</script>

<style lang="less" scoped></style>
