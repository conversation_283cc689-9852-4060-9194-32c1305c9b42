<template>
    <div class="chart-b relative">
        <div ref="chartContainer" class="gauge-container">
            <!-- -->
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { pieData } from '../device/const'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import 'echarts-liquidfill'
import _cloneDeep from 'lodash/cloneDeep'

const { t, locale } = useI18n()
const props = defineProps({
    soc: {
        type: Number,
        default: 0,
    },
})

const chartContainer = ref()
const myChart = ref()

const updateChart = () => {
    if (!chartContainer.value) return

    const soc = props.soc || 0
    const option = _cloneDeep(pieData)
    const num = soc / 100

    if (soc < 10) {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#FF4D4F']
        option.series[1].itemStyle.color = '#FF4D4F'
    } else if (soc >= 10 && soc < 30) {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#FD750B']
        option.series[1].itemStyle.color = '#FD750B '
    } else {
        option.series[0].axisLine.lineStyle.color[0] = [num, '#3EDACD']
        option.series[1].itemStyle.color = '#3EDACD'
    }

    if (!soc) {
        option.series[1].amplitude = 0
        option.series[1].waveAnimation = 0
    }

    // 设置数据和标签
    option.series[1].data = [num, num]
    option.series[1].label.formatter = `${soc}%`
    option.series[1].label.fontSize = 20
    option.series[0].data[0].value = soc
    option.series[0].data[0].detail.formatter = t('station_shengyudianliang')
    console.log(myChart.value)
    // 初始化或更新图表
    if (!myChart.value) {
        myChart.value = echarts.init(chartContainer.value)
    }
    myChart.value.setOption(option, true)
}

const initChart = async () => {
    await nextTick()
    updateChart()
}

// 监听soc变化
watch(
    () => props.soc,
    (val) => {
        if (val) {
            console.log(val, '2232323')
            updateChart()
        }
    }
    // { immediate: true, deep: true }
)

// // 监听语言变化
// watch(
//     () => locale.value,
//     () => {
//         updateChart()
//     }
// )

onMounted(() => {
    console.log(1231231231)
    // myChart.value = echarts.init(chartContainer.value)
    initChart()
    window.addEventListener('resize', updateChart())
})

onUnmounted(() => {
    if (myChart.value) {
        myChart.value.dispose()
        myChart.value = null
    }
})
</script>

<style lang="less" scoped>
.chart-b {
    width: 50%;
    min-width: 200px;
    &::after {
        display: block;
        content: '';
        width: 100%;
        padding-top: 100%;
    }
    margin: 0 auto;
}
.gauge-container {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 19;
}
</style>
