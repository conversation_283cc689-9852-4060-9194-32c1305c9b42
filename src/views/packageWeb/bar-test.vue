<template>
    <div class="bar-test-container">
        <h2>Bar组件测试</h2>

        <!-- 组件展示 -->
        <div class="chart-section">
            <h3>充放电柱状图</h3>
            <div class="chart-wrapper">
                <Bar ref="barChart" :data="chartData" :height="400" />
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>控制面板</h3>

            <div class="control-group">
                <h4>数据操作</h4>
                <div class="buttons">
                    <button @click="generateWeekData">生成周数据</button>
                    <button @click="generateMonthData">生成月数据</button>
                    <button @click="generateYearData">生成年数据</button>
                    <button @click="addRandomData">添加随机数据</button>
                    <button @click="clearData">清空数据</button>
                </div>
            </div>

            <div class="control-group">
                <h4>组件方法测试</h4>
                <div class="buttons">
                    <button @click="switchToCapacity">切换到电量视图</button>
                    <button @click="switchToDuration">切换到时长视图</button>
                    <button @click="getComponentInfo">获取组件信息</button>
                    <button @click="refreshChart">刷新图表</button>
                    <button @click="testGoAlarm">跳转到告警页面</button>
                </div>
            </div>

            <div class="info-display">
                <h4>组件状态信息</h4>
                <div class="info-item">
                    <span>当前视图:</span>
                    <span class="value">{{ componentInfo.currentView }}</span>
                </div>
                <div class="info-item">
                    <span>当前时间段:</span>
                    <span class="value">{{ componentInfo.currentPeriod }}</span>
                </div>
                <div class="info-item">
                    <span>时间参数:</span>
                    <span class="value">{{
                        JSON.stringify(componentInfo.timeParams)
                    }}</span>
                </div>
                <div class="info-item">
                    <span>数据数量:</span>
                    <span class="value">{{ chartData.length }}</span>
                </div>
            </div>
        </div>

        <!-- 数据预览 -->
        <div class="data-preview">
            <h3>当前数据预览</h3>
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>充电电量(kWh)</th>
                            <th>放电电量(kWh)</th>
                            <th>充电时长(h)</th>
                            <th>放电时长(h)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in chartData" :key="index">
                            <td>{{ item.date }}</td>
                            <td>{{ item.chargeCap }}</td>
                            <td>{{ item.dischargeCap }}</td>
                            <td>{{ item.chargeDur }}</td>
                            <td>{{ item.dischargeDur }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="usage-guide">
            <h3>使用说明</h3>
            <div class="guide-content">
                <h4>功能特性</h4>
                <ul>
                    <li>✅ 支持充放电电量和时长两种视图切换</li>
                    <li>✅ 支持近七日、近一月、近一年时间段选择</li>
                    <li>✅ 自动计算时间参数</li>
                    <li>✅ 响应式图表布局</li>
                    <li>✅ 国际化支持</li>
                </ul>

                <h4>Props</h4>
                <ul>
                    <li><code>data</code>: Array - 数据数组</li>
                    <li><code>height</code>: Number - 图表高度，默认400</li>
                    <li>
                        <code>width</code>: String/Number - 图表宽度，默认'100%'
                    </li>
                </ul>

                <h4>暴露的方法</h4>
                <ul>
                    <li><code>updateChart()</code> - 更新图表</li>
                    <li><code>switchView(view)</code> - 切换视图</li>
                    <li><code>getCurrentView()</code> - 获取当前视图</li>
                    <li><code>getCurrentPeriod()</code> - 获取当前时间段</li>
                    <li>
                        <code>getCurrentTimeParams()</code> - 获取当前时间参数
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Bar from './bar.vue'
import dayjs from 'dayjs'

const barChart = ref()
const chartData = ref([])
const componentInfo = ref({
    currentView: '',
    currentPeriod: '',
    timeParams: {},
})

// 生成随机数据
const generateRandomData = (count, dateStart) => {
    const data = []
    for (let i = 0; i < count; i++) {
        const date = dayjs(dateStart).add(i, 'day').format('YYYY-MM-DD')
        data.push({
            date,
            chargeCap: Number((Math.random() * 50 + 50).toFixed(1)),
            dischargeCap: Number((Math.random() * 40 + 40).toFixed(1)),
            chargeDur: Number((Math.random() * 4 + 4).toFixed(1)),
            dischargeDur: Number((Math.random() * 3 + 3).toFixed(1)),
        })
    }
    return data
}

// 生成周数据
const generateWeekData = () => {
    const startDate = dayjs().subtract(6, 'day')
    chartData.value = generateRandomData(7, startDate)
}

// 生成月数据
const generateMonthData = () => {
    const startDate = dayjs().subtract(29, 'day')
    chartData.value = generateRandomData(30, startDate)
}

// 生成年数据（按月）
const generateYearData = () => {
    const data = []
    for (let i = 11; i >= 0; i--) {
        const date = dayjs().subtract(i, 'month').format('YYYY-MM')
        data.push({
            date,
            chargeCap: Number((Math.random() * 500 + 1000).toFixed(1)),
            dischargeCap: Number((Math.random() * 400 + 800).toFixed(1)),
            chargeDur: Number((Math.random() * 100 + 200).toFixed(1)),
            dischargeDur: Number((Math.random() * 80 + 150).toFixed(1)),
        })
    }
    chartData.value = data
}

// 添加随机数据
const addRandomData = () => {
    const newItem = {
        date: dayjs().add(chartData.value.length, 'day').format('YYYY-MM-DD'),
        chargeCap: Number((Math.random() * 50 + 50).toFixed(1)),
        dischargeCap: Number((Math.random() * 40 + 40).toFixed(1)),
        chargeDur: Number((Math.random() * 4 + 4).toFixed(1)),
        dischargeDur: Number((Math.random() * 3 + 3).toFixed(1)),
    }
    chartData.value.push(newItem)
}

// 清空数据
const clearData = () => {
    chartData.value = []
}

// 切换到电量视图
const switchToCapacity = () => {
    barChart.value?.switchView('capacity')
    updateComponentInfo()
}

// 切换到时长视图
const switchToDuration = () => {
    barChart.value?.switchView('duration')
    updateComponentInfo()
}

// 获取组件信息
const getComponentInfo = () => {
    updateComponentInfo()
}

// 刷新图表
const refreshChart = () => {
    barChart.value?.updateChart()
}

// 测试跳转到告警页面
const testGoAlarm = () => {
    barChart.value?.goAlarm('test-device-sn')
}

// 更新组件信息
const updateComponentInfo = () => {
    if (barChart.value) {
        componentInfo.value = {
            currentView: barChart.value.getCurrentView(),
            currentPeriod: barChart.value.getCurrentPeriod(),
            timeParams: barChart.value.getCurrentTimeParams(),
        }
    }
}

onMounted(() => {
    // 初始化数据
    generateWeekData()

    // 延迟获取组件信息，确保组件已初始化
    setTimeout(() => {
        updateComponentInfo()
    }, 1000)
})
</script>

<style lang="less" scoped>
.bar-test-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;

    h2,
    h3,
    h4 {
        color: #333;
        margin-bottom: 15px;
    }

    .chart-section {
        margin-bottom: 40px;

        .chart-wrapper {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }
    }

    .control-panel {
        background: #f5f5f5;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;

        .control-group {
            margin-bottom: 20px;

            .buttons {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
                margin-bottom: 15px;

                button {
                    padding: 8px 16px;
                    border: 1px solid #3edacd;
                    background: white;
                    color: #3edacd;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        background: #3edacd;
                        color: white;
                    }
                }
            }
        }

        .info-display {
            background: white;
            padding: 15px;
            border-radius: 4px;

            .info-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid #eee;

                &:last-child {
                    border-bottom: none;
                }

                .value {
                    font-weight: bold;
                    color: #3edacd;
                    max-width: 60%;
                    word-break: break-all;
                }
            }
        }
    }

    .data-preview {
        margin-bottom: 30px;

        .data-table {
            overflow-x: auto;

            table {
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                th,
                td {
                    padding: 12px;
                    text-align: left;
                    border-bottom: 1px solid #eee;
                }

                th {
                    background: #f5f5f5;
                    font-weight: bold;
                    color: #333;
                }

                tr:hover {
                    background: #f9f9f9;
                }
            }
        }
    }

    .usage-guide {
        background: #f5f5f5;
        padding: 20px;
        border-radius: 8px;

        .guide-content {
            ul {
                padding-left: 20px;

                li {
                    margin-bottom: 8px;
                    line-height: 1.5;

                    code {
                        background: #e9ecef;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}
</style>
