<template>
    <div class="distribution-test-container">
        <h2>Distribution组件测试</h2>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-group">
                <label>数据类型:</label>
                <select v-model="currentType" @change="generateData">
                    <option value="voltage">电压 (Voltage)</option>
                    <option value="temperature">温度 (Temperature)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>行数 (Y轴数量):</label>
                <input 
                    type="number" 
                    v-model.number="yCount" 
                    min="1" 
                    max="10"
                    @change="generateData"
                />
            </div>
            
            <div class="control-group">
                <label>数据数量:</label>
                <input 
                    type="number" 
                    v-model.number="dataCount" 
                    min="4" 
                    max="100"
                    @change="generateData"
                />
            </div>
            
            <div class="buttons">
                <button @click="generateData">重新生成数据</button>
                <button @click="addRandomData">添加随机数据</button>
                <button @click="clearData">清空数据</button>
            </div>
        </div>
        
        <!-- 图表展示区域 -->
        <div class="charts-section">
            <!-- 电压分布图 -->
            <div class="chart-wrapper">
                <h3>电压分布热力图</h3>
                <Distribution 
                    :data="voltageData" 
                    type="voltage" 
                    :yCount="2"
                    :height="250"
                />
                <p class="data-info">
                    数据: {{ voltageData.slice(0, 10).join(', ') }}{{ voltageData.length > 10 ? '...' : '' }}
                    (共{{ voltageData.length }}个数据点)
                </p>
            </div>
            
            <!-- 温度分布图 -->
            <div class="chart-wrapper">
                <h3>温度分布热力图</h3>
                <Distribution 
                    :data="temperatureData" 
                    type="temperature" 
                    :yCount="2"
                    :height="250"
                />
                <p class="data-info">
                    数据: {{ temperatureData.slice(0, 10).join(', ') }}{{ temperatureData.length > 10 ? '...' : '' }}
                    (共{{ temperatureData.length }}个数据点)
                </p>
            </div>
            
            <!-- 动态图表 -->
            <div class="chart-wrapper">
                <h3>动态图表 ({{ currentType === 'voltage' ? '电压' : '温度' }})</h3>
                <Distribution 
                    :data="dynamicData" 
                    :type="currentType" 
                    :yCount="yCount"
                    :height="300"
                />
                <p class="data-info">
                    当前类型: {{ currentType === 'voltage' ? '电压 (V)' : '温度 (°C)' }} | 
                    行数: {{ yCount }} | 
                    数据数量: {{ dynamicData.length }}
                </p>
            </div>
        </div>
        
        <!-- 颜色对比 -->
        <div class="color-comparison">
            <h3>颜色对比</h3>
            <div class="color-samples">
                <div class="color-group">
                    <h4>电压颜色 (Voltage)</h4>
                    <div class="color-bar">
                        <div class="color-item" style="background: #D4F6FB">低</div>
                        <div class="color-item" style="background: #A4DBE6"></div>
                        <div class="color-item" style="background: #6FBECE"></div>
                        <div class="color-item" style="background: #5FA7BA"></div>
                        <div class="color-item" style="background: #204765">高</div>
                    </div>
                </div>
                
                <div class="color-group">
                    <h4>温度颜色 (Temperature)</h4>
                    <div class="color-bar">
                        <div class="color-item" style="background: #F3E49F">低</div>
                        <div class="color-item" style="background: #EAC391"></div>
                        <div class="color-item" style="background: #C45054">高</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Distribution from './distribution.vue'

// 控制参数
const currentType = ref('voltage')
const yCount = ref(2)
const dataCount = ref(20)

// 数据
const voltageData = ref([])
const temperatureData = ref([])
const dynamicData = ref([])

// 生成电压数据 (3.0-4.2V)
const generateVoltageData = (count) => {
    return Array.from({ length: count }, () => 
        Number((Math.random() * 1.2 + 3.0).toFixed(3))
    )
}

// 生成温度数据 (20-60°C)
const generateTemperatureData = (count) => {
    return Array.from({ length: count }, () => 
        Number((Math.random() * 40 + 20).toFixed(1))
    )
}

// 生成数据
const generateData = () => {
    // 确保数据数量能被行数整除
    const adjustedCount = Math.floor(dataCount.value / yCount.value) * yCount.value
    
    if (currentType.value === 'voltage') {
        dynamicData.value = generateVoltageData(adjustedCount)
    } else {
        dynamicData.value = generateTemperatureData(adjustedCount)
    }
}

// 添加随机数据
const addRandomData = () => {
    const newData = currentType.value === 'voltage' 
        ? generateVoltageData(yCount.value)
        : generateTemperatureData(yCount.value)
    
    dynamicData.value = [...dynamicData.value, ...newData]
}

// 清空数据
const clearData = () => {
    dynamicData.value = []
}

onMounted(() => {
    // 初始化固定数据
    voltageData.value = [
        3.1, 3.2, 3.0, 3.3, 3.1, 3.2, 3.4, 3.0, 3.2, 3.1,
        3.3, 3.1, 3.2, 3.4, 3.0, 3.2, 3.1, 3.3, 3.2, 3.0
    ]
    
    temperatureData.value = [
        25, 26, 24, 27, 25, 26, 28, 24, 26, 25,
        27, 25, 26, 28, 24, 26, 25, 27, 26, 24
    ]
    
    // 生成动态数据
    generateData()
})
</script>

<style lang="less" scoped>
.distribution-test-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    
    h2, h3, h4 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .control-panel {
        background: #f5f5f5;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
        
        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
            
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #666;
            }
            
            select, input {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        }
        
        .buttons {
            margin-top: 15px;
            
            button {
                padding: 8px 16px;
                margin-right: 10px;
                border: 1px solid #3EDACD;
                background: white;
                color: #3EDACD;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;
                
                &:hover {
                    background: #3EDACD;
                    color: white;
                }
            }
        }
    }
    
    .charts-section {
        .chart-wrapper {
            margin-bottom: 40px;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            background: white;
            
            .data-info {
                margin-top: 10px;
                padding: 10px;
                background: #f9f9f9;
                border-radius: 4px;
                font-size: 12px;
                color: #666;
                word-break: break-all;
            }
        }
    }
    
    .color-comparison {
        background: #f5f5f5;
        padding: 20px;
        border-radius: 8px;
        
        .color-samples {
            display: flex;
            gap: 40px;
            
            .color-group {
                flex: 1;
                
                .color-bar {
                    display: flex;
                    border-radius: 4px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    
                    .color-item {
                        flex: 1;
                        padding: 10px;
                        text-align: center;
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                    }
                }
            }
        }
    }
}
</style>
