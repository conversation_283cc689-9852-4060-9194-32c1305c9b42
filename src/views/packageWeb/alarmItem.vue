<template>
    <div class="alarm-item">
        <div class="alarm-content">
            <!-- 告警头部 -->
            <div class="alarm-header">
                <div class="alarm-info">
                    <span
                        v-if="alarmLeveStatus[data.alarmLevel]?.title"
                        class="alarm-level"
                        :style="{
                            color: getColor(data.alarmLevel),
                            background: getBgColor(data.alarmLevel),
                        }"
                    >
                        {{ alarmLeveStatus[data.alarmLevel]?.title }}
                    </span>
                    <span class="alarm-title">{{ data?.alarmDesc }}</span>
                </div>

                <!-- 操作按钮 -->
                <div class="alarm-actions" v-if="data.stage === 'current'">
                    <el-button
                        type="danger"
                        size="small"
                        @click="clearAlarm(data)"
                        :loading="isClearing"
                    >
                        清除
                    </el-button>
                </div>
            </div>

            <!-- 告警详情 -->
            <div class="alarm-details">
                <div class="detail-item">
                    <span class="label">设备类型：</span>
                    <span class="value">{{
                        getDeviceTypeName(data.deviceType) || 'BMS'
                    }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">设备编号：</span>
                    <span class="value">{{ data.deviceSn }}</span>
                </div>
                <div class="detail-item">
                    <span class="label">发生时间：</span>
                    <span class="value">{{ formatTime(data.alarmTime) }}</span>
                </div>
                <div class="detail-item" v-if="data.stationName">
                    <span class="label">所属站点：</span>
                    <span class="value">
                        {{ data.stationName }}
                        {{ data?.containerNo ? '#' + data.containerNo : '' }}
                    </span>
                </div>
            </div>
        </div>

        <!-- 确认对话框 -->
        <el-dialog
            v-model="showConfirmDialog"
            title="确认操作"
            width="400px"
            center
        >
            <div class="dialog-content">
                <el-icon class="warning-icon"><Warning /></el-icon>
                <p>确定要清除当前告警吗？</p>
            </div>

            <template #footer>
                <el-button @click="dialogCancel">取消</el-button>
                <el-button
                    type="danger"
                    @click="dialogConfirm"
                    :loading="isClearing"
                >
                    确认清除
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import service from '@/apiService/device'
import dayjs from 'dayjs'

const emits = defineEmits(['refresh'])
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})

// 设备类型映射
const deviceTypeMap = {
    SXBLQ: 'PCS',
    DUI: '堆',
    CU: '簇',
    YLJ: '液冷机',
    DI: 'DI',
    DB: '电表',
    EMSCTRL: 'EMS',
    KT: '液冷机',
    FIRE: '消防',
}

// 告警级别配置
const alarmLeveStatus = {
    1: {
        title: '次要',
        color: 'rgba(24, 144, 255, 1)',
        background: 'rgba(230, 247, 255, 1)',
    },
    2: {
        title: '重要',
        color: 'rgba(253, 117, 11, 1)',
        background: 'rgba(253, 117, 11, 0.10)',
    },
    3: {
        title: '紧急',
        color: 'rgba(255, 77, 79, 1)',
        background: 'rgba(255, 77, 79, 0.10)',
    },
}

// 响应式数据
const showConfirmDialog = ref(false)
const clearAlarmId = ref(null)
const isClearing = ref(false)

// 获取设备类型名称
const getDeviceTypeName = (type) => {
    return deviceTypeMap[type] || type
}

// 格式化时间
const formatTime = (time) => {
    if (!time) return '-'
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 清除告警API
const clearAlarmApi = async (alarmId) => {
    try {
        isClearing.value = true
        const {
            data: { code },
        } = await service.clearAlarm({ alarmId })

        if (code === 0) {
            ElMessage.success('清除成功')
            setTimeout(() => {
                emits('refresh', true)
            }, 300)
        } else {
            ElMessage.error('清除失败')
        }
    } catch (error) {
        console.error('清除告警失败:', error)
        ElMessage.error('清除失败')
    } finally {
        isClearing.value = false
        showConfirmDialog.value = false
    }
}

// 清除告警
const clearAlarm = (row) => {
    clearAlarmId.value = row.id
    showConfirmDialog.value = true
}

// 取消对话框
const dialogCancel = () => {
    showConfirmDialog.value = false
    clearAlarmId.value = null
}

// 确认清除
const dialogConfirm = () => {
    if (clearAlarmId.value) {
        clearAlarmApi(clearAlarmId.value)
    }
}

// 获取告警级别颜色
const getColor = (key) => {
    return alarmLeveStatus[key]?.color || '#666'
}

// 获取告警级别背景色
const getBgColor = (key) => {
    return alarmLeveStatus[key]?.background || '#f5f5f5'
}
</script>

<style lang="less" scoped>
.alarm-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .alarm-content {
        padding: 20px;

        .alarm-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;

            .alarm-info {
                display: flex;
                align-items: center;
                flex: 1;
                gap: 12px;

                .alarm-level {
                    display: inline-block;
                    padding: 4px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                    white-space: nowrap;
                }

                .alarm-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    line-height: 1.4;
                    word-break: break-word;
                }
            }

            .alarm-actions {
                margin-left: 12px;
            }
        }

        .alarm-details {
            .detail-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-size: 14px;
                line-height: 1.5;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    color: #666;
                    min-width: 80px;
                    font-weight: 500;
                }

                .value {
                    color: #333;
                    word-break: break-word;
                }
            }
        }
    }

    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 0;

        .warning-icon {
            font-size: 48px;
            color: #f56c6c;
            margin-bottom: 16px;
        }

        p {
            margin: 0;
            font-size: 16px;
            color: #333;
            line-height: 1.5;
        }
    }
}
</style>
