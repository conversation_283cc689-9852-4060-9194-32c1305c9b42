<template>
    <div class="h5-container">
        <div class="web">
            <div class="deviceInfo">
                <div class="car-img">
                    <carImg :vehicleType="'forklift'" />
                </div>
                <div class="car-info">
                    <div class="no">{{ '车辆001' }}</div>
                    <div class="sn">设备编号：{{ basicInfo.bmsInfo.sn }}</div>
                </div>
            </div>
            <div class="content">
                <el-tabs v-model="activeKey" @tab-change="handleTabChange">
                    <template #default>
                        <el-tab-pane :label="$t('实时状态')" name="A">
                            <div class="swiper-item">
                                <div class="sw-item" id="slide-A">
                                    <div class="real-info">
                                        <div class="device-state">
                                            <div
                                                class="flex items-center gap-x-1"
                                                :style="{
                                                    color: getState(
                                                        realData.status || 0,
                                                        'power'
                                                    ).color,
                                                }"
                                            >
                                                <i
                                                    :class="[
                                                        'iconfont',
                                                        getState(
                                                            realData.status ||
                                                                0,
                                                            'power'
                                                        ).icon,
                                                    ]"
                                                    :style="{
                                                        color: getState(
                                                            realData.status ||
                                                                0,
                                                            'power'
                                                        ).color,
                                                    }"
                                                ></i>
                                                <span
                                                    class="text-title dark:text-title-dark"
                                                >
                                                    {{
                                                        getState(
                                                            realData.status ||
                                                                0,
                                                            'power'
                                                        ).label
                                                            ? $t(
                                                                  getState(
                                                                      realData.status ||
                                                                          0,
                                                                      'power'
                                                                  ).label
                                                              )
                                                            : '-'
                                                    }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center">
                                            <!-- <img
                                        src="@/static/4g.svg"
                                        class="iconSvg iconfont"
                                    /> -->
                                            <span class="ml-2">
                                                信号强度：{{
                                                    realData.signal4g || '-'
                                                }}
                                            </span>
                                        </div>
                                        <div
                                            v-if="
                                                basicInfo.productModel
                                                    .bmsSummaryInfo.totalAlarms
                                            "
                                            @click="goAlarm"
                                        >
                                            <div
                                                class="flex items-center"
                                                style="color: #fd0b0b"
                                            >
                                                <i
                                                    class="iconfont error icon-ica-dianchi-guzhang"
                                                    style="color: #fd0b0b"
                                                ></i>
                                                <span class="ml-2">
                                                    异常
                                                    {{
                                                        basicInfo.productModel
                                                            .bmsSummaryInfo
                                                            .totalAlarms
                                                    }}
                                                    条
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="guage-chart">
                                        <gauge :soc="29" />
                                    </div>
                                    <div
                                        class="timeAndAddress flex items-center"
                                    >
                                        <div class="flex items-center">
                                            <!-- <img
                                        src="@/static/time.svg"
                                        class="iconSvg iconfont"
                                    /> -->
                                            <span class="ml-2">
                                                {{
                                                    dayjs(
                                                        realData.time * 1000
                                                    ).format(
                                                        'YYYY/MM/DD HH:mm:ss'
                                                    )
                                                }}
                                            </span>
                                        </div>
                                        <div class="flex items-center ml-5">
                                            <!-- <img
                                        src="@/static/dingwei.svg"
                                        class="iconSvg"
                                    /> -->
                                            <span class="ml-2">{{
                                                realData.address || '-'
                                            }}</span>
                                        </div>
                                    </div>
                                    <div class="statistic">
                                        <div class="statistic-content">
                                            <div class="statistic-item">
                                                <div class="num">
                                                    {{ realData.sysVoltage }}
                                                </div>
                                                <div class="name">
                                                    总电压(V)
                                                </div>
                                            </div>
                                            <div class="statistic-item">
                                                <div class="num">
                                                    {{ realData.sysCurrent }}
                                                </div>
                                                <div class="name">
                                                    总电流(A)
                                                </div>
                                            </div>
                                            <div class="statistic-item">
                                                <div class="num">
                                                    {{ realData.soh }}%
                                                </div>
                                                <div class="name">
                                                    电池健康度
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-line"></div>
                                    <div class="dis-title">
                                        <div class="mt-6 mb-3">
                                            电芯单体详情
                                        </div>
                                        <!-- 电压 -->
                                        <div>
                                            <div class="dis-sub-title">
                                                电压热力分布
                                            </div>
                                            <div class="distributed-chart">
                                                <distribution
                                                    :data="realCells.voltage"
                                                    type="voltage"
                                                    :yCount="2"
                                                />
                                            </div>
                                            <div class="distributed">
                                                <div
                                                    class="distributed-content"
                                                >
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.volAvg
                                                            }}V
                                                        </div>
                                                        <div class="name">
                                                            平均值
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.volMax
                                                            }}V/#{{
                                                                realData.volMaxId
                                                            }}
                                                        </div>
                                                        <div class="name">
                                                            最大值/电芯编号
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.volMin
                                                            }}V/#{{
                                                                realData.volMinId
                                                            }}
                                                        </div>
                                                        <div class="name">
                                                            最小值/电芯编号
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 温度 -->
                                        <div class="mt-4">
                                            <div class="dis-sub-title">
                                                温度热力分布
                                            </div>
                                            <div class="distributed-chart-temp">
                                                <temp-distribution
                                                    :data="
                                                        realCells.temperature
                                                    "
                                                    type="temperature"
                                                    :yCount="1"
                                                />
                                            </div>
                                            <div class="distributed">
                                                <div
                                                    class="distributed-content"
                                                >
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.tempAvg
                                                            }}°C
                                                        </div>
                                                        <div class="name">
                                                            平均值
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.tempMax
                                                            }}°C/#{{
                                                                realData.tempMaxId
                                                            }}
                                                        </div>
                                                        <div class="name">
                                                            最大值/传感器编号
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="distributed-item"
                                                    >
                                                        <div class="num">
                                                            {{
                                                                realData.tempMin
                                                            }}°C/#{{
                                                                realData.tempMinId
                                                            }}
                                                        </div>
                                                        <div class="name">
                                                            最小值/传感器编号
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane :label="$t('Basic Info')" name="B">
                            <div class="swiper-item">
                                <div class="" id="slide-B">
                                    <div class="basic-info">
                                        <div class="info-box">
                                            <div class="title b">
                                                服务商：{{ '万家乐公司' }}
                                            </div>
                                            <div class="title b mb-2">
                                                客户名称：{{
                                                    basicInfo.productModel.majorCustomers.join(
                                                        '、'
                                                    )
                                                }}
                                            </div>
                                            <div class="title">
                                                累计循环次数：{{
                                                    realData.cycleCount
                                                }}
                                            </div>
                                            <div class="title">
                                                累计充电时间：{{
                                                    basicInfo.productModel
                                                        .bmsSummaryInfo
                                                        .chgTimeSum
                                                }}
                                                h
                                            </div>
                                            <div class="title">
                                                累计放电时间：{{
                                                    basicInfo.productModel
                                                        .bmsSummaryInfo
                                                        .dsgTimeSum
                                                }}
                                                h
                                            </div>
                                            <div class="title">
                                                累计充电容量：{{
                                                    basicInfo.productModel
                                                        .bmsSummaryInfo
                                                        .chgCapSum
                                                }}
                                                Ah
                                            </div>
                                            <div class="title">
                                                累计放电容量：{{
                                                    basicInfo.productModel
                                                        .bmsSummaryInfo
                                                        .dsgCapSum
                                                }}
                                                Ah
                                            </div>
                                        </div>
                                        <div class="info-box">
                                            <div class="title b mb-2">
                                                电芯信息
                                            </div>
                                            <div class="title">
                                                车辆类型：{{ vehicleType }}
                                            </div>
                                            <div class="title">
                                                电芯类型：{{
                                                    basicInfo.productModel
                                                        .cellType
                                                }}
                                            </div>
                                            <div class="title">
                                                电芯封装：{{
                                                    basicInfo.productModel
                                                        .cellPack
                                                }}
                                            </div>
                                            <div class="title">
                                                电芯串数：{{
                                                    realData.cellNub || 0
                                                }}
                                            </div>
                                            <div class="title">
                                                电池箱数：{{
                                                    basicInfo.productModel.tanks
                                                }}
                                            </div>
                                            <div class="title">
                                                单体电压：{{
                                                    basicInfo.productModel
                                                        .cellVoltage
                                                }}
                                                V
                                            </div>
                                            <div class="title">
                                                额定总压：{{
                                                    basicInfo.productModel
                                                        .ratedVoltage
                                                }}
                                                V
                                            </div>
                                            <div class="title">
                                                额定总流：{{
                                                    basicInfo.productModel
                                                        .ratedCurrent
                                                }}
                                                A
                                            </div>
                                            <div class="title">
                                                额定容量：{{
                                                    basicInfo.productModel
                                                        .ratedCapacity || 0
                                                }}
                                                Ah
                                            </div>
                                            <div class="title">
                                                额定能量：{{
                                                    basicInfo.productModel
                                                        .ratedEnergy
                                                }}
                                                kWh
                                            </div>
                                        </div>
                                        <div class="info-box">
                                            <div class="title b mb-2">
                                                基础信息
                                            </div>
                                            <div class="title">
                                                设备编号：{{
                                                    basicInfo.bmsInfo.sn
                                                }}
                                            </div>
                                            <div class="title">
                                                电池编号：{{
                                                    basicInfo.bmsInfo
                                                        .batteryNo || '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                设备型号：{{
                                                    basicInfo.productModel
                                                        .model || '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                HWID：{{
                                                    basicInfo.bmsInfo.hwid ||
                                                    '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                IMEI：{{
                                                    basicInfo.bmsInfo.imei ||
                                                    '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                软件版本：{{
                                                    basicInfo.bmsInfo
                                                        .softwareVersion || '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                出厂日期：{{ '-' }}
                                            </div>
                                            <div class="title">
                                                激活日期：{{
                                                    basicInfo.bmsInfo.activeTime
                                                        ? dayjs(
                                                              basicInfo.bmsInfo
                                                                  .activeTime
                                                          ).format('YYYY/MM/DD')
                                                        : '-'
                                                }}
                                            </div>
                                            <div class="title">
                                                服务到期日期：{{
                                                    basicInfo.bmsInfo
                                                        .serviceExpireDate
                                                        ? dayjs(
                                                              basicInfo.bmsInfo
                                                                  .serviceExpireDate
                                                          ).format('YYYY/MM/DD')
                                                        : '-'
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane :label="$t('历史数据')" name="C">
                            <div class="swiper-item">
                                <div class="sw-item" id="slide-C">
                                    <div
                                        class="div charge-title flex justify-between"
                                    >
                                        <div class="div charge-title-l">
                                            <span class="name"
                                                >昨日充电时长</span
                                            >
                                            <div
                                                class="div flex items-baseline leading-5"
                                            >
                                                <span
                                                    class="text-base font-bold ml-1 charge-num"
                                                >
                                                    {{
                                                        chargeStatisticsData.yesterdayChargeDur
                                                    }}
                                                </span>
                                                <span
                                                    class="text-xs charge-unit"
                                                >
                                                    h
                                                </span>
                                            </div>
                                        </div>
                                        <div
                                            class="div charge-title-r h-12 leading-12 rounded-r text-sm font-medium text-right"
                                        >
                                            <span
                                                class="leading-tight text-left name"
                                            >
                                                昨日放电时长
                                            </span>
                                            <div
                                                class="div flex items-baseline leading-5"
                                            >
                                                <span
                                                    class="text-base font-bold ml-1 charge-num"
                                                >
                                                    {{
                                                        chargeStatisticsData.yesterdayDischargeDur
                                                    }}
                                                </span>
                                                <span
                                                    class="text-xs charge-unit"
                                                    >h</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="history">
                                        <div
                                            class="history-title flex justify-between items-center"
                                        >
                                            <div class="flex items-center">
                                                <span>
                                                    {{
                                                        chargeStatisticsData?.beforeYesterdayChargeDur ==
                                                        0
                                                            ? '前一日无数据'
                                                            : '较前一日' + '：'
                                                    }}
                                                </span>
                                                <percentage
                                                    :num="
                                                        chargeStatisticsData.comparedChargePercent
                                                    "
                                                    class="ml-3"
                                                />
                                            </div>
                                            <div class="flex items-center">
                                                <span>
                                                    {{
                                                        chargeStatisticsData?.beforeYesterdayDischargeDur ==
                                                        0
                                                            ? '前一日无数据'
                                                            : '较前一日' + '：'
                                                    }}
                                                </span>
                                                <percentage
                                                    :num="
                                                        chargeStatisticsData.comparedDischargePercent
                                                    "
                                                    class="ml-3"
                                                />
                                            </div>
                                        </div>
                                        <div
                                            class="flex justify-between items-center px-3 total"
                                        >
                                            <div class="flex justify-start">
                                                <div class="mr-4">
                                                    <div class="mb-4">
                                                        月累计(h)
                                                    </div>
                                                    <div class="b">
                                                        {{
                                                            chargeStatisticsData.currentMonthChargeDur
                                                        }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="mb-4">
                                                        总计(h)
                                                    </div>
                                                    <div class="b">
                                                        {{
                                                            chargeStatisticsData.totalChargeDur
                                                        }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end text-right"
                                            >
                                                <div class="mr-4">
                                                    <div class="mb-4">
                                                        月累计(h)
                                                    </div>
                                                    <div class="b">
                                                        {{
                                                            chargeStatisticsData.currentMonthDischargeDur
                                                        }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="mb-4">
                                                        总计(h)
                                                    </div>
                                                    <div class="b">
                                                        {{
                                                            chargeStatisticsData.totalDischargeDur
                                                        }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <bar ref="barRef" />
                                    <div class="mt-6 pb-8">
                                        <div class="mb-4 px-4">运行记录</div>
                                        <div class="run-title">
                                            <div class="run-title-time">
                                                时间
                                            </div>
                                            <div class="run-title-status">
                                                运行状态
                                            </div>
                                            <div class="run-title-soc">SOC</div>
                                            <div class="run-title-cap">
                                                充/放电量
                                            </div>
                                        </div>
                                        <div
                                            v-for="(item, index) in runningData"
                                            :key="index"
                                            class="run-list run-title"
                                        >
                                            <div class="run-title-time">
                                                <div>
                                                    {{
                                                        formatterDate(
                                                            item.startTime,
                                                            item.endTime
                                                        )
                                                    }}
                                                </div>
                                                <div>
                                                    {{
                                                        formatterTime(
                                                            item.startTime,
                                                            item.endTime
                                                        )
                                                    }}
                                                </div>
                                            </div>
                                            <div class="run-title-status">
                                                <span
                                                    class="y"
                                                    :class="
                                                        item.chargeStatus === 1
                                                            ? 'green'
                                                            : 'blue'
                                                    "
                                                ></span>
                                                <span>{{
                                                    item.chargeStatus === 1
                                                        ? '充电'
                                                        : '放电'
                                                }}</span>
                                            </div>
                                            <div class="run-title-soc">
                                                {{ item.startSoc || 0 }}% -
                                                {{ item.endSoc || 0 }}%
                                            </div>
                                            <div class="run-title-cap">
                                                {{
                                                    item.chargeStatus == 1
                                                        ? item.chargeQuantity
                                                        : item.dischargeQuantity
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                    </template>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { useRoute } from 'vue-router'
import carImg from '@/views/device/car/components/carImg.vue'
import { getState } from '@/common/util'
import percentage from './percentage.vue'
import powerApi from '@/apiService/power'
import api from '@/apiService/index'
import dayjs from 'dayjs'
import store from '@/store'
import gauge from './gauge.vue'
import bar from './bar.vue'
import distribution from './distribution.vue'
import tempDistribution from './tempDistribution.vue'
// 获取路由参数
const route = useRoute()
const pageInfo = ref({
    sn: route.query.sn || '866597074507136', // 从路由参数获取设备编号，如果没有则使用默认值
})

const activeKey = ref('A')
const onChange = (val, ind) => {
    console.log('val,ind', val, ind)
    activeKey.value = val
}

const tabOptions = ref([
    {
        label: '实时状态',
        id: 'A',
    },
    {
        label: '基础信息',
        id: 'B',
    },
    {
        label: '运行历史',
        id: 'C',
    },
])

// H5环境下的swiper高度管理

// 使用reactive定义基础信息对象
const basicInfo = reactive({
    bmsInfo: {
        sn: '',
        batteryNo: '',
        hwid: '',
        imei: '',
        softwareVersion: '',
        activeTime: null,
        serviceExpireDate: null,
        projectId: '',
    },
    productModel: {
        majorCustomers: [],
        bmsSummaryInfo: {
            totalAlarms: 0,
            chgTimeSum: 0,
            dsgTimeSum: 0,
            chgCapSum: 0,
            dsgCapSum: 0,
        },
        cellType: '',
        cellPack: '',
        tanks: 0,
        cellVoltage: 0,
        ratedVoltage: 0,
        ratedCurrent: 0,
        ratedCapacity: 0,
        ratedEnergy: 0,
        model: '',
        vehicleType: '',
    },
})

const getBasicInfo = async () => {
    try {
        let res = await powerApi.getBasicInfo({ sn: pageInfo.value.sn })
        // 使用Object.assign更新reactive对象
        Object.assign(basicInfo, res.data.data)
    } catch (error) {
        console.error('获取基础信息失败:', error)
    }
}

// 使用reactive定义实时数据对象
const realData = reactive({
    status: 0,
    signal4g: '',
    soc: 0,
    time: 0,
    address: '',
    sysVoltage: 0,
    sysCurrent: 0,
    soh: 0,
    volAvg: 0,
    volMax: 0,
    volMaxId: 0,
    volMin: 0,
    volMinId: 0,
    tempAvg: 0,
    tempMax: 0,
    tempMaxId: 0,
    tempMin: 0,
    tempMinId: 0,
    cycleCount: 0,
})

const getRealData = async () => {
    try {
        let res = await powerApi.getRealData({ sn: pageInfo.value.sn })
        // 使用Object.assign更新reactive对象
        Object.assign(realData, res.data.data)
    } catch (error) {
        console.error('获取实时数据失败:', error)
    }
}

// 使用reactive定义电芯数据对象
const realCells = reactive({
    voltage: [],
    temperature: [],
})

const getRealCells = async () => {
    try {
        let res = await powerApi.getRealCells({ sn: pageInfo.value.sn })
        // 使用Object.assign更新reactive对象
        Object.assign(realCells, res.data.data)
    } catch (error) {
        console.error('获取电芯数据失败:', error)
    }
}

const vehicleTypeOptions = computed(() => {
    return store.state.dictionary.dictionaries.vehicleType || []
})
const vehicleType = computed(() => {
    console.log(vehicleTypeOptions.value)
    console.log(basicInfo.productModel.vehicleType)
    if (!basicInfo.productModel.vehicleType) return '-'
    const item = vehicleTypeOptions.value.find(
        (item) => item.value == basicInfo.productModel.vehicleType
    )
    if (!item) return '-'
    return item.label
})

const chargeStatisticsData = reactive({
    beforeYesterdayChargeDur: void 0,
    beforeYesterdayDischargeDur: void 0,
    comparedChargePercent: void 0,
    comparedDischargePercent: void 0,
    currentMonthChargeDur: void 0,
    currentMonthDischargeDur: void 0,
    totalChargeDur: void 0,
    totalDischargeDur: void 0,
    yesterdayChargeDur: void 0,
    yesterdayDischargeDur: void 0,
})

const statsPowerBattDurUsageSummary = async () => {
    try {
        let res = await powerApi.statsPowerBattDurUsageSummary({
            sn: pageInfo.value.sn,
            projectId: basicInfo.bmsInfo.projectId || undefined,
        })
        Object.assign(chargeStatisticsData, res.data.data)
    } catch (error) {
        console.error('获取充电统计数据失败:', error)
    }
}

const runningData = ref([])
const getPowerBmsChargeRecordPageList = async () => {
    try {
        let res = await powerApi.getPowerBmsChargeRecordPageList({
            size: 10,
            sn: pageInfo.value.sn,
            current: 1,
            chargeStatus: '',
        })
        runningData.value = res.data.data.records
    } catch (error) {
        console.error('获取运行记录失败:', error)
    }
}

const formatterDate = (startTime, endTime) => {
    const start = dayjs(startTime).format('MMDD')
    const end = dayjs(endTime).format('MMDD')
    if (start == end) {
        return dayjs(startTime).format('MM/DD')
    } else {
        return (
            dayjs(startTime).format('MM/DD') +
            '-' +
            dayjs(endTime).format('MM/DD')
        )
    }
}

const formatterTime = (startTime, endTime) => {
    const start = dayjs(startTime).format('MMDD')
    const end = dayjs(endTime).format('MMDD')
    if (start == end) {
        return (
            dayjs(startTime).format('HH:mm') +
            '-' +
            dayjs(endTime).format('HH:mm')
        )
    } else {
        return (
            dayjs(startTime).format('HH:mm') +
            '-次日' +
            dayjs(endTime).format('HH:mm')
        )
    }
}
const barRef = ref()
const handleTabChange = () => {
    barRef.value.updateChart()
}

onMounted(async () => {
    try {
        await store.dispatch('dictionary/getDictionary', 'vehicleType')
        await getBasicInfo()
        await getRealData()
        await getRealCells()
        // 统计
        await statsPowerBattDurUsageSummary()
        await getPowerBmsChargeRecordPageList()
    } catch (error) {
        console.error('页面初始化失败:', error)
    }
})
</script>

<style lang="less" scoped>
.h5-container {
    flex: 1;
}
.web {
    width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    max-width: 1080px;
    margin: 0 auto;
}

.deviceInfo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px 15px;
    background: #fff;
    margin-bottom: 10px;
    width: 100%;
}

.car-img {
    width: 131px;
    height: 78px;
}

.car-info {
    flex: 1;
    text-align-last: left;
    margin-left: 34px;
    .no {
        font-size: 14px;
        color: #6fbece;
        line-height: 24px;
    }
    .sn {
        font-size: 12px;
        color: rgba(34, 34, 34, 0.8);
        line-height: 17px;
    }
}

.content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    width: 100%;
}

.scroll-view {
    background: #fff;
}

.tabs {
    width: 100%;
    padding: 12px;
    padding-bottom: 0;
    white-space: nowrap;
    display: flex;
    line-height: 20px;
    justify-content: space-between;
    position: relative;
    &::after {
        display: block;
        content: '';
        width: 100%;
        height: 1px;
        background: rgba(0, 0, 0, 0.08);
        position: absolute;
        left: 0;
        bottom: 2px;
        z-index: 1;
    }
    .tab {
        padding-bottom: 8px;
        color: #999;
        text-align: center;
        position: relative;
        z-index: 2;
        cursor: pointer;
        transition: color 0.3s ease;
        &.selected {
            color: #1890ff;
            position: relative;
            font-weight: bold;
            &::after {
                content: '';
                height: 4px;
                width: 24px;
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                background-color: #1890ff;
                border-radius: 2px;
            }
        }
    }
}

.swiper-box {
    overflow-y: auto;
    flex: 1;
}

.swiper {
    width: 100%;
    overflow-y: auto;
}

.swiper-item {
    width: 100%;
}

.sw-item {
    padding: 0 12px;
}

.real-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    flex-wrap: wrap;
    gap: 10px;
}

.statistic {
    padding-top: 8px;
    padding-bottom: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    margin-top: 8px;
}

.statistic-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .statistic-item {
        text-align: center;
        position: relative;
        width: 33.333%;

        & + .statistic-item {
            border-left: 1px solid #e8e8e8;
            margin: 0;
        }

        .num {
            font-size: 16px;
            line-height: 24px;
            font-weight: bold;
            color: #1e312b;
        }

        .name {
            font-size: 12px;
            line-height: 20px;
            color: rgba(30, 49, 43, 0.6);
        }
    }
}

.bg-line {
    width: 100%;
    height: 14px;
    background: rgba(34, 34, 34, 0.08);
    margin: 16px 0;
}

.distributed {
    padding-top: 12px;
    padding-bottom: 12px;
    background: rgba(34, 34, 34, 0.08);
}

.distributed-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .distributed-item {
        text-align: center;
        position: relative;
        width: 33.333%;

        & + .distributed-item {
            border-left: 1px solid rgba(0, 0, 0, 0.08);
            margin: 0;
        }

        .num {
            font-size: 16px;
            line-height: 24px;
            font-weight: bold;
            color: #1e312b;
        }

        .name {
            font-size: 12px;
            line-height: 20px;
            color: rgba(30, 49, 43, 0.6);
        }
    }
}

.distributed-chart {
    width: 100%;
}

.distributed-chart-temp {
    width: 100%;
}

#slide-A {
    padding-bottom: 20px;
    padding-top: 16px;
}

#slide-C {
    padding-top: 16px;
}

.basic-info {
    background: #f6f6f6;
    padding: 12px;
    margin-bottom: 12px;
}

.info-box {
    background: #fff;
    border-radius: 4px;
    padding: 12px 14px;
    & + .info-box {
        margin-top: 12px;
    }
    .title {
        font-size: 14px;
        line-height: 24px;
        color: rgba(34, 34, 34, 0.8);
        &.b {
            font-weight: bold;
            color: #222222;
        }
    }
}

.charge-title {
    position: relative;
    left: -3px;
    margin-bottom: 7px;
    display: flex;
    justify-content: space-between;
    color: #333;
    .name {
        font-size: 14px;
        line-height: 20px;
    }
}

.charge-title-l,
.charge-title-r {
    width: calc(50% - 14px);
    height: 28px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    font-weight: 500;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);
    padding-left: 12px;
    justify-content: flex-start;
    border-radius: 4px 0 0 4px;

    > .div:first-child {
        line-height: 24px;
        font-size: 16px;
    }

    > .div:nth-child(2) {
        font-weight: bold;
    }

    > .div:last-child {
        font-size: 12px;
        line-height: 20px;
        margin-left: 2px;
    }

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 28px solid rgba(51, 190, 79, 0.1);
        border-right: 24px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);
    padding-right: 12px;
    justify-content: flex-end;
    border-radius: 0 4px 4px 0;

    > .div:first-child {
        line-height: 24px;
        font-size: 16px;
    }

    > .div:nth-child(2) {
        font-size: 35px;
        line-height: 32px;
        font-weight: bold;
        margin-left: 18px;
    }

    > .div:last-child {
        font-size: 12px;
        line-height: 20px;
        margin-left: 2px;
    }

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 28px solid rgba(119, 155, 219, 0.1);
        border-left: 24px solid transparent;
    }
}

.charge-num {
    letter-spacing: 0;
    line-height: 1;
}

.charge-unit {
    vertical-align: text-bottom;
    margin-left: 4px;
}

.history {
    font-size: 12px;
    margin-bottom: 12px;
}

.history-title {
    padding: 10px 6px;
    border-bottom: 1px solid rgba(119, 155, 219, 0.1);
    margin-bottom: 6px;
}

.run-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgba(34, 34, 34, 0.65);
    font-size: 12px;
    line-height: 16px;
    padding: 0 8px;
    .run-title-time {
        width: 100px;
        text-align: left;
    }
    .run-title-status {
        width: 75px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        .y {
            width: 10px;
            height: 10px;
            display: block;
            border-radius: 50%;
            background: transparent;
            box-shadow: 0px 0px 4px 0px rgba(51, 190, 79, 0.2);
            border: 1px solid #ffffff;
            margin-right: 6px;
            &.blue {
                background: #4b82ff;
            }
            &.green {
                background: #33be4f;
            }
        }
    }
    .run-title-soc {
        width: 95px;
        text-align: center;
    }
    .run-title-cap {
        width: 80px;
        text-align: right;
    }
}

.run-list {
    padding: 10px;
    border-radius: 8px;
    background: rgba(34, 34, 34, 0.04);
    margin-top: 8px;
}

.iconSvg {
    width: 14px;
    height: 14px;
    margin-left: 6px;
}

.dis-title {
    color: var(--text-80);
}

.dis-sub-title {
    color: var(--text-60);
    margin-bottom: 16px;
}

.total {
    color: rgba(34, 34, 34, 0.8);
    .b {
        font-weight: 500;
    }
}

/* Tailwind CSS 工具类 */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.gap-x-1 {
    column-gap: 0.25rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 0.75rem;
}

.ml-5 {
    margin-left: 1.25rem;
}

.mr-4 {
    margin-right: 1rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.pb-8 {
    padding-bottom: 2rem;
}

.text-right {
    text-align: right;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.text-2\.5xl {
    font-size: 1.75rem;
    line-height: 2rem;
}

.font-bold {
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

:deep(.el-tabs__nav) {
    width: 100%;
}
:deep(.el-tabs__item) {
    flex: 1;
}
:deep(.el-tabs__active-bar) {
    width: 36px !important;
    left: calc(16.6667% - 35px);
}
</style>
