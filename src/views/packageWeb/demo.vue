<template>
    <div class="demo-container">
        <div class="header">
            <h1>小程序转H5页面演示</h1>
            <p>这是将小程序样式转换为H5页面的演示</p>
        </div>

        <div class="demo-links">
            <div class="link-card">
                <h3>原始小程序版本</h3>
                <p>使用小程序特有的标签和样式单位</p>
                <router-link to="/packageWeb/index" class="btn btn-primary">
                    查看小程序版本
                </router-link>
            </div>

            <div class="link-card">
                <h3>H5适配版本</h3>
                <p>转换为标准HTML标签和CSS样式</p>
                <router-link to="/packageWeb/h5" class="btn btn-success">
                    查看H5版本
                </router-link>
            </div>
        </div>

        <div class="features">
            <h2>转换特性</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h4>标签转换</h4>
                    <ul>
                        <li>view → div</li>
                        <li>text → span</li>
                        <li>image → img</li>
                        <li>scroll-view → div</li>
                        <li>swiper → 自定义轮播</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>样式适配</h4>
                    <ul>
                        <li>rpx → px 单位转换</li>
                        <li>响应式布局优化</li>
                        <li>H5兼容性处理</li>
                        <li>移动端适配</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>功能保持</h4>
                    <ul>
                        <li>图表组件正常显示</li>
                        <li>数据交互完整</li>
                        <li>路由参数支持</li>
                        <li>API调用正常</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h4>代码优化</h4>
                    <ul>
                        <li>移除小程序特有API</li>
                        <li>使用Vue Router</li>
                        <li>错误处理增强</li>
                        <li>性能优化</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="usage">
            <h2>使用说明</h2>
            <div class="usage-content">
                <p>
                    1. <strong>访问H5版本：</strong>直接访问
                    <code>/packageWeb/h5</code> 路由
                </p>
                <p>
                    2. <strong>传递参数：</strong>通过URL参数传递设备编号，如
                    <code>/packageWeb/h5?sn=866597074507136</code>
                </p>
                <p>
                    3.
                    <strong>移动端适配：</strong
                    >页面已针对移动端进行优化，支持触摸操作
                </p>
                <p>
                    4.
                    <strong>图表显示：</strong
                    >所有图表组件都已适配H5环境，无需额外配置
                </p>
            </div>
        </div>
    </div>
</template>

<script setup>
// 演示页面，无需复杂逻辑
</script>

<style lang="less" scoped>
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        sans-serif;
}

.header {
    text-align: center;
    margin-bottom: 40px;

    h1 {
        color: #1890ff;
        margin-bottom: 10px;
        font-size: 2.5rem;
    }

    p {
        color: #666;
        font-size: 1.1rem;
    }
}

.demo-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.link-card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;

    h3 {
        color: #333;
        margin-bottom: 12px;
    }

    p {
        color: #666;
        margin-bottom: 20px;
    }
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    &.btn-primary {
        background: #1890ff;
        color: white;

        &:hover {
            background: #40a9ff;
        }
    }

    &.btn-success {
        background: #52c41a;
        color: white;

        &:hover {
            background: #73d13d;
        }
    }
}

.features {
    margin-bottom: 40px;

    h2 {
        color: #333;
        margin-bottom: 24px;
        text-align: center;
    }
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;

    h4 {
        color: #1890ff;
        margin-bottom: 12px;
    }

    ul {
        list-style: none;
        padding: 0;

        li {
            padding: 4px 0;
            color: #666;
            position: relative;
            padding-left: 16px;

            &::before {
                content: '•';
                color: #1890ff;
                position: absolute;
                left: 0;
            }
        }
    }
}

.usage {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
        color: #333;
        margin-bottom: 20px;
    }
}

.usage-content {
    p {
        margin-bottom: 12px;
        line-height: 1.6;
        color: #666;

        strong {
            color: #333;
        }

        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
            color: #d73a49;
        }
    }
}

@media (max-width: 768px) {
    .demo-container {
        padding: 16px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .demo-links {
        grid-template-columns: 1fr;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }
}
</style>
