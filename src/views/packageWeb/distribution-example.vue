<template>
    <div class="distribution-example">
        <h2>Distribution组件使用示例</h2>
        
        <!-- 实际使用场景 -->
        <div class="usage-scenarios">
            <!-- 电池管理系统 -->
            <div class="scenario">
                <h3>电池管理系统 - 电芯电压分布</h3>
                <div class="scenario-content">
                    <div class="chart-area">
                        <Distribution 
                            :data="batteryVoltages" 
                            type="voltage" 
                            :yCount="4"
                            :height="300"
                        />
                    </div>
                    <div class="info-panel">
                        <h4>电池组信息</h4>
                        <ul>
                            <li>总电芯数: {{ batteryVoltages.length }}</li>
                            <li>排列方式: 4行 × {{ Math.floor(batteryVoltages.length / 4) }}列</li>
                            <li>电压范围: {{ minVoltage }}V - {{ maxVoltage }}V</li>
                            <li>平均电压: {{ avgVoltage }}V</li>
                            <li>电压差: {{ voltageRange }}V</li>
                        </ul>
                        <div class="status" :class="batteryStatus.class">
                            {{ batteryStatus.text }}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 温度监控系统 -->
            <div class="scenario">
                <h3>温度监控系统 - 传感器温度分布</h3>
                <div class="scenario-content">
                    <div class="chart-area">
                        <Distribution 
                            :data="temperatureSensors" 
                            type="temperature" 
                            :yCount="3"
                            :height="300"
                        />
                    </div>
                    <div class="info-panel">
                        <h4>温度监控信息</h4>
                        <ul>
                            <li>传感器数量: {{ temperatureSensors.length }}</li>
                            <li>排列方式: 3行 × {{ Math.floor(temperatureSensors.length / 3) }}列</li>
                            <li>温度范围: {{ minTemp }}°C - {{ maxTemp }}°C</li>
                            <li>平均温度: {{ avgTemp }}°C</li>
                            <li>温度差: {{ tempRange }}°C</li>
                        </ul>
                        <div class="status" :class="tempStatus.class">
                            {{ tempStatus.text }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 实时数据模拟 -->
        <div class="realtime-section">
            <h3>实时数据模拟</h3>
            <div class="controls">
                <button @click="toggleSimulation" :class="{ active: isSimulating }">
                    {{ isSimulating ? '停止模拟' : '开始模拟' }}
                </button>
                <button @click="resetData">重置数据</button>
                <span class="update-info">更新间隔: 2秒</span>
            </div>
            
            <div class="realtime-charts">
                <div class="realtime-chart">
                    <h4>实时电压监控</h4>
                    <Distribution 
                        :data="realtimeVoltages" 
                        type="voltage" 
                        :yCount="2"
                        :height="250"
                    />
                </div>
                
                <div class="realtime-chart">
                    <h4>实时温度监控</h4>
                    <Distribution 
                        :data="realtimeTemperatures" 
                        type="temperature" 
                        :yCount="2"
                        :height="250"
                    />
                </div>
            </div>
        </div>
        
        <!-- 代码示例 -->
        <div class="code-examples">
            <h3>代码示例</h3>
            
            <div class="code-block">
                <h4>基本用法</h4>
                <pre><code>&lt;template&gt;
  &lt;Distribution 
    :data="voltageData" 
    type="voltage" 
    :yCount="2"
    :height="250"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import Distribution from './distribution.vue'

const voltageData = ref([3.1, 3.2, 3.0, 3.3, 3.1, 3.2, 3.4, 3.0])
&lt;/script&gt;</code></pre>
            </div>
            
            <div class="code-block">
                <h4>温度类型使用</h4>
                <pre><code>&lt;template&gt;
  &lt;Distribution 
    :data="temperatureData" 
    type="temperature" 
    :yCount="3"
    :height="300"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
const temperatureData = ref([25, 26, 24, 27, 25, 26, 28, 24, 26])
&lt;/script&gt;</code></pre>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Distribution from './distribution.vue'

// 电池电压数据 (20个电芯，4行5列)
const batteryVoltages = ref([])

// 温度传感器数据 (18个传感器，3行6列)
const temperatureSensors = ref([])

// 实时数据
const realtimeVoltages = ref([])
const realtimeTemperatures = ref([])
const isSimulating = ref(false)
let simulationTimer = null

// 计算属性 - 电池状态
const minVoltage = computed(() => Math.min(...batteryVoltages.value).toFixed(3))
const maxVoltage = computed(() => Math.max(...batteryVoltages.value).toFixed(3))
const avgVoltage = computed(() => {
    const sum = batteryVoltages.value.reduce((a, b) => a + b, 0)
    return (sum / batteryVoltages.value.length).toFixed(3)
})
const voltageRange = computed(() => (maxVoltage.value - minVoltage.value).toFixed(3))

const batteryStatus = computed(() => {
    const range = parseFloat(voltageRange.value)
    if (range > 0.2) {
        return { class: 'danger', text: '电压差过大，需要均衡' }
    } else if (range > 0.1) {
        return { class: 'warning', text: '电压差较大，建议检查' }
    } else {
        return { class: 'normal', text: '电压分布正常' }
    }
})

// 计算属性 - 温度状态
const minTemp = computed(() => Math.min(...temperatureSensors.value).toFixed(1))
const maxTemp = computed(() => Math.max(...temperatureSensors.value).toFixed(1))
const avgTemp = computed(() => {
    const sum = temperatureSensors.value.reduce((a, b) => a + b, 0)
    return (sum / temperatureSensors.value.length).toFixed(1)
})
const tempRange = computed(() => (maxTemp.value - minTemp.value).toFixed(1))

const tempStatus = computed(() => {
    const max = parseFloat(maxTemp.value)
    const range = parseFloat(tempRange.value)
    
    if (max > 50 || range > 15) {
        return { class: 'danger', text: '温度过高或分布不均' }
    } else if (max > 40 || range > 10) {
        return { class: 'warning', text: '温度偏高，需要关注' }
    } else {
        return { class: 'normal', text: '温度分布正常' }
    }
})

// 生成初始数据
const generateInitialData = () => {
    // 生成20个电芯电压数据 (3.0-4.2V)
    batteryVoltages.value = Array.from({ length: 20 }, () => 
        Number((Math.random() * 1.2 + 3.0).toFixed(3))
    )
    
    // 生成18个温度传感器数据 (20-45°C)
    temperatureSensors.value = Array.from({ length: 18 }, () => 
        Number((Math.random() * 25 + 20).toFixed(1))
    )
    
    // 生成实时数据
    realtimeVoltages.value = Array.from({ length: 12 }, () => 
        Number((Math.random() * 1.2 + 3.0).toFixed(3))
    )
    
    realtimeTemperatures.value = Array.from({ length: 12 }, () => 
        Number((Math.random() * 25 + 20).toFixed(1))
    )
}

// 更新实时数据
const updateRealtimeData = () => {
    // 模拟电压微小变化
    realtimeVoltages.value = realtimeVoltages.value.map(voltage => {
        const change = (Math.random() - 0.5) * 0.02 // ±0.01V变化
        return Number(Math.max(3.0, Math.min(4.2, voltage + change)).toFixed(3))
    })
    
    // 模拟温度变化
    realtimeTemperatures.value = realtimeTemperatures.value.map(temp => {
        const change = (Math.random() - 0.5) * 2 // ±1°C变化
        return Number(Math.max(15, Math.min(60, temp + change)).toFixed(1))
    })
}

// 切换模拟状态
const toggleSimulation = () => {
    if (isSimulating.value) {
        clearInterval(simulationTimer)
        simulationTimer = null
        isSimulating.value = false
    } else {
        simulationTimer = setInterval(updateRealtimeData, 2000)
        isSimulating.value = true
    }
}

// 重置数据
const resetData = () => {
    generateInitialData()
}

onMounted(() => {
    generateInitialData()
})

onUnmounted(() => {
    if (simulationTimer) {
        clearInterval(simulationTimer)
    }
})
</script>

<style lang="less" scoped>
.distribution-example {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    
    h2, h3, h4 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .usage-scenarios {
        .scenario {
            margin-bottom: 40px;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
            
            h3 {
                background: #f5f5f5;
                padding: 15px 20px;
                margin: 0;
                border-bottom: 1px solid #eee;
            }
            
            .scenario-content {
                display: flex;
                padding: 20px;
                gap: 20px;
                
                .chart-area {
                    flex: 2;
                }
                
                .info-panel {
                    flex: 1;
                    background: #f9f9f9;
                    padding: 20px;
                    border-radius: 8px;
                    
                    ul {
                        list-style: none;
                        padding: 0;
                        
                        li {
                            padding: 5px 0;
                            border-bottom: 1px solid #eee;
                            
                            &:last-child {
                                border-bottom: none;
                            }
                        }
                    }
                    
                    .status {
                        margin-top: 15px;
                        padding: 10px;
                        border-radius: 4px;
                        text-align: center;
                        font-weight: bold;
                        
                        &.normal {
                            background: #d4edda;
                            color: #155724;
                        }
                        
                        &.warning {
                            background: #fff3cd;
                            color: #856404;
                        }
                        
                        &.danger {
                            background: #f8d7da;
                            color: #721c24;
                        }
                    }
                }
            }
        }
    }
    
    .realtime-section {
        margin-bottom: 40px;
        
        .controls {
            margin-bottom: 20px;
            
            button {
                padding: 8px 16px;
                margin-right: 10px;
                border: 1px solid #3EDACD;
                background: white;
                color: #3EDACD;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;
                
                &:hover, &.active {
                    background: #3EDACD;
                    color: white;
                }
            }
            
            .update-info {
                color: #666;
                font-size: 14px;
            }
        }
        
        .realtime-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            
            .realtime-chart {
                border: 1px solid #eee;
                border-radius: 8px;
                padding: 20px;
                background: white;
            }
        }
    }
    
    .code-examples {
        .code-block {
            margin-bottom: 20px;
            
            pre {
                background: #f5f5f5;
                padding: 15px;
                border-radius: 4px;
                overflow-x: auto;
                
                code {
                    font-family: 'Courier New', monospace;
                    font-size: 14px;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>
