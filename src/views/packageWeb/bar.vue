<template>
    <div id="ttjbgnm"><!-- -->1</div>
</template>

<script setup>
import { ref } from 'vue'
import { getChargeOption, updateEcharts } from '@/views/device/const'
import { useI18n } from 'vue-i18n'
import _cloneDeep from 'lodash/cloneDeep'

const { t, locale } = useI18n()
const statsPowerBattDailyUsageData = ref([])
const chargeTotalDateType = ref({
    periodType: 'day',
})
const setDeviceStatusStatisticsCharts = (id) => {
    if (!statsPowerBattDailyUsageData.value?.length) {
        return
    }
    let x
    x = statsPowerBattDailyUsageData.value.map((item) => item.date)
    if (chargeTotalDateType.value.periodType == 'day') {
        x = statsPowerBattDailyUsageData.value.map((item) => item.date.slice(5))
    }
    const y1 = statsPowerBattDailyUsageData.value.map((item) => item.chargeDur)
    const y2 = statsPowerBattDailyUsageData.value.map(
        (item) => item.dischargeDur
    )
    const y3 = statsPowerBattDailyUsageData.value.map((item) => item.chargeCap)
    const y4 = statsPowerBattDailyUsageData.value.map(
        (item) => item.dischargeCap
    )
    let options = _cloneDeep(getChargeOption())
    options.legend.top = '12px'
    options.grid.bottom = '10px'
    if (x.length >= 25) {
        options.series[0].barWidth = '12px'
        options.series[1].barWidth = '12px'
    } else if (x.length >= 10) {
        options.series[0].barWidth = '25px'
        options.series[1].barWidth = '25px'
    } else {
        options.series[0].barWidth = '30px'
        options.series[1].barWidth = '30px'
    }
    options.xAxis.data = x
    options.yAxis.name = id == 'runningDuration' ? 'h' : 'Ah'
    options.series[0].data = y3
    options.series[1].data = y4
    options.tooltip.formatter = function (params) {
        return (
            params[0].name +
            '<br/>' +
            params[0].marker +
            params[0].seriesName +
            ' : ' +
            (params[0].value || 0) +
            'Ah' +
            '<br/>' +
            params[1].marker +
            params[1].seriesName +
            ' : ' +
            (params[1].value || 0) +
            'Ah'
        )
    }
    if (id == 'runningDuration') {
        options.series[0].data = y1
        options.series[1].data = y2
        options.series[0].name = t('Charging duration')
        options.series[1].name = t('Discharging duration')
        options.legend.data = [
            t('Charging duration'),
            t('Discharging duration'),
        ]
        options.tooltip.formatter = function (params) {
            return (
                params[0].name +
                '<br/>' +
                params[0].marker +
                params[0].seriesName +
                ' : ' +
                (params[0].value || 0) +
                'h' +
                '<br/>' +
                params[1].marker +
                params[1].seriesName +
                ' : ' +
                (params[1].value || 0) +
                'h'
            )
        }
    }

    updateEcharts(id, options)
}
</script>

<style lang="less" scoped></style>
