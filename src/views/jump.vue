<template>
    <div
        class="flex flex-col items-center justify-center min-h-screen text-center"
    >
        <p class="text-lg font-semibold mb-4">正在跳转，请稍候...</p>
        <p v-if="error" class="text-red-500">{{ error }}</p>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'

const route = useRoute()
const error = ref('')

const isWeChat = () => {
    return /micromessenger/i.test(navigator.userAgent)
}

const loadWxConfig = async (url) => {
    try {
        // 请求你的后端服务生成微信JS SDK配置参数
        const res = await axios.get('/api/wx-config', {
            params: {
                url: encodeURIComponent(url),
            },
        })

        const config = res.data

        wx.config({
            debug: false,
            appId: 'wxd1b8a814502a9a16',
            timestamp: Date.now(),
            nonceStr: '1',
            signature: '1',
            jsApiList: ['checkJsApi'],
        })

        wx.ready(() => {
            // 判断是否在微信小程序外部
            if (window.__wxjs_environment !== 'miniprogram') {
                wx.miniProgram.navigateTo({
                    url: `/packageDevice/detail?id=${route.query.id}`,
                })
            }
        })

        wx.error((err) => {
            error.value = '微信SDK初始化失败，请稍后重试。'
            console.error('wx.error:', err)
        })
    } catch (err) {
        error.value = '获取微信配置失败。'
        console.error('获取微信配置失败:', err)
    }
}

onMounted(() => {
    const id = route.query.id
    if (!id) {
        error.value = '缺少设备ID参数'
        return
    }

    const currentUrl = window.location.href.split('#')[0]

    if (isWeChat()) {
        // 微信内环境，尝试跳转小程序
        loadWxConfig(currentUrl)
    } else {
        // 非微信环境，跳转到H5页面
        window.location.href = `https://abc.com/device/detail?id=${id}`
    }
})
</script>

<style scoped>
body {
    background-color: #f9fafb;
}
</style>
