<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
        @close="closeDrawer"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('changeUsername') }}</span>
                </div>
                <div class="flex gap-x-3">
                    <el-button plain round @click="closeDrawer">{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button plain round @click="onConfirm" type="primary">{{
                        $t('ConfirmTheChanges')
                    }}</el-button>
                </div>
            </div>
        </template>

        <el-form
            ref="ruleFormRef"
            style="max-width: 600px"
            :model="formState"
            :rules="stepTwoFormRules"
            label-width="auto"
            class="formState"
            hide-required-asterisk
            :status-icon="false"
        >
            <el-form-item :label="$t('username') + ':'" prop="realName">
                <el-input
                    v-model="formState.realName"
                    :placeholder="$t('placeholder_qingshuru')"
                    :maxlength="20"
                />
            </el-form-item>
        </el-form>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/apiService/index'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentUsername: {
        type: String,
        default: '',
    },
})
watch(
    () => props.currentUsername,
    (newVal) => {
        formState.realName = newVal
    }
)

const emit = defineEmits(['update:visible', 'confirm'])

// 计算属性用于双向绑定visible
const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    },
})

// 表单ref
const ruleFormRef = ref(null)

// 表单数据
const formState = reactive({
    realName: props.currentUsername,
})

// 表单校验规则
const stepTwoFormRules = {
    realName: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        {
            min: 1,
            max: 20,
            message: t('chuangdu_tips'.replace('s%', 1).replace('e%', 20)),
            trigger: 'blur',
        },
    ],
}

// 确认修改
const onConfirm = async () => {
    if (!ruleFormRef.value) return

    await ruleFormRef.value.validate(async (valid, fields) => {
        if (valid) {
            let res = await api.updateMeInfo({ realName: formState.realName })
            if (res.data.data) {
                ElMessage.success('修改成功')
                emit('confirm')
            }
        } else {
            console.log('表单验证失败:', fields)
        }
    })
}
const closeDrawer = () => {
    // 重置表单和状态
    formState.realName = props.currentUsername
    emit('update:visible', false)
}
</script>

<style scoped></style>
