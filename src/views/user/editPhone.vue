<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
        @close="closeDrawer"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('ChangePhone') }}</span>
                </div>
                <div class="flex gap-x-3" v-if="status === 1">
                    <el-button plain round @click="closeDrawer">{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button plain round type="primary" @click="nextStep">{{
                        $t('NextStep')
                    }}</el-button>
                </div>
                <div class="flex gap-x-3" v-if="status === 2">
                    <el-button plain round @click="prevStep">{{
                        $t('PreviousStep')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        type="primary"
                        @click="confirmChange"
                        >{{ $t('ConfirmTheChanges') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div v-if="status === 1">
            <certification
                :currentPhone="props.currentPhone"
                :currentEmail="props.currentEmail"
                @update:authType="handleAuthTypeUpdate"
                @update:code="handleCodeUpdate"
            />
        </div>

        <div v-if="status === 2">
            <el-form
                ref="stepTwoFormRef"
                style="max-width: 600px"
                :model="stepTwoForm"
                :rules="stepTwoFormRules"
                label-width="auto"
                class="stepTwoForm"
                hide-required-asterisk
                :status-icon="false"
            >
                <el-form-item
                    :label="$t('NewPhone') + ':'"
                    prop="newPhone"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.newPhone"
                        :placeholder="$t('placeholder_qingshuru')"
                    >
                        <template #prefix>
                            <div>+86</div>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item
                    :label="$t('Captcha') + ':'"
                    prop="captcha"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.captcha"
                        :placeholder="$t('placeholder_qingshuru')"
                        :maxlength="4"
                    >
                        <template #append>
                            <el-button
                                plain
                                type="primary"
                                @click="sendCaptcha(2)"
                                :disabled="isCaptchaDisabled2"
                            >
                                {{ captchaButtonText2 }}
                            </el-button>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import service from '@/apiService/device'
import api from '@/apiService/index'
import { useI18n } from 'vue-i18n'
import certification from './certification.vue'
const { t } = useI18n()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentPhone: {
        type: String,
        default: '',
    },
    currentEmail: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update:visible', 'confirm'])
// 计算属性用于双向绑定visible
const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    },
})

// 当前步骤状态
const status = ref(1)

// 表单ref
const stepOneFormRef = ref(null)
const stepTwoFormRef = ref(null)

// 第一步表单数据
const stepOneForm = reactive({
    captcha: '',
})

// 第二步表单数据
const stepTwoForm = reactive({
    newPhone: '',
    captcha: '',
})

// 验证手机号的正则表达式
const phoneReg = /^1[3-9]\d{9}$/

// 表单验证规则
const stepOneFormRules = {
    captcha: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        { min: 4, max: 4, message: '', trigger: 'blur' },
    ],
}

const stepTwoFormRules = {
    newPhone: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        {
            pattern: phoneReg,
            message: t('Please enter a valid phone number'),
            trigger: 'blur',
        },
    ],
    captcha: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        { min: 4, max: 4, message: t('Captcha_tips01'), trigger: 'blur' },
    ],
}

// 验证码按钮相关
const isCaptchaDisabled = ref(false)
const isCaptchaDisabled2 = ref(false)
const captchaButtonText = ref(t('GetVerificationCode'))
const captchaButtonText2 = ref(t('GetVerificationCode'))
let timer = null
let timer2 = null

// 发送验证码
const sendCaptcha = async (step) => {
    const phone = step === 1 ? props.currentPhone : stepTwoForm.newPhone
    if (step === 2 && !phoneReg.test(phone)) {
        ElMessage.error(t('Please enter a valid phone number'))
        return
    }
    // 这里添加发送验证码的接口调用
    if (stepTwoForm.newPhone === props.currentPhone) {
        ElMessage.error(t('phone_tips02'))
        return
    }
    await service.sendSmsCode({
        phone: phone,
        smsVerifyType: 'updatePhone',
    })
    isCaptchaDisabled2.value = true
    let count = 60
    timer2 = setInterval(() => {
        if (count > 0) {
            captchaButtonText2.value = t('Captcha_tips02').replace('s%', count)
            count--
        } else {
            clearInterval(timer2)
            captchaButtonText2.value = t('GetVerificationCode')
            isCaptchaDisabled2.value = false
        }
    }, 1000)
}

const authType = ref('sms')
const verificationCode = ref('')

// 处理certification组件的事件
const handleAuthTypeUpdate = (value) => {
    authType.value = value
}

const handleCodeUpdate = (value) => {
    verificationCode.value = value
}

// 下一步
const nextStep = async () => {
    if (!verificationCode.value) {
        ElMessage.error(t('placeholder_qingshuru'))
        return
    }

    try {
        // 验证码验证
        let res = await service.secondAuth({
            phone: authType.value === 'sms' ? props.currentPhone : undefined,
            email: authType.value === 'email' ? props.currentEmail : undefined,
            code: verificationCode.value,
            authType: authType.value,
        })
        if (res.data.data) {
            status.value = 2
            verificationCode.value = ''
        }
    } catch (error) {
        ElMessage.error(t('Verification failed'))
    }
}

// 上一步
const prevStep = () => {
    status.value = 1
    stepTwoForm.newPhone = ''
    stepTwoForm.captcha = ''
}

// 确认修改
const confirmChange = async () => {
    if (!stepTwoFormRef.value) return
    if (stepTwoForm.newPhone === props.currentPhone) {
        ElMessage.error(t('phone_tips02'))
        return
    }
    await stepTwoFormRef.value.validate(async (valid) => {
        if (valid) {
            // 先验证是否是正确的验证码
            // 再调取修改手机号的接口
            let res1 = await api.updateMeInfo({
                phone: stepTwoForm.newPhone,
                code: stepTwoForm.captcha,
            })
            if (res1.data.code === 0) {
                ElMessage.success(t('caozuochenggong'))
                emit('confirm')
                closeDrawer()
            }
            // 这里添加修改手机号的接口调用
            //
        }
    })
}

// 关闭抽屉
const closeDrawer = () => {
    // 重置表单和状态
    // if (status.value === 1) stepOneFormRef.value.resetFields()
    if (status.value === 2) stepTwoFormRef.value.resetFields()
    status.value = 1
    stepOneForm.captcha = ''
    stepTwoForm.newPhone = ''
    stepTwoForm.captcha = ''
    if (timer) clearInterval(timer)
    if (timer2) clearInterval(timer2)
    captchaButtonText.value = t('GetVerificationCode')
    captchaButtonText2.value = t('GetVerificationCode')
    isCaptchaDisabled.value = false
    isCaptchaDisabled2.value = false

    emit('update:visible', false)
}
</script>

<style scoped></style>
