<template>
    <div class="viewScreen flex flex-col" id="full">
        <div class="text-center h2 title-header">
            <!-- 储能数字驾驶舱 -->
            <img src="@/assets/fullScreen/bgAll/bg-6.png" />
        </div>
        <section class="blockMeta flex">
            <div class="flex flex-col">
                <SubTitle class="mb-4" width="82%">电量指标</SubTitle>
                <div class="flex flex-grow">
                    <div>
                        <p class="text-center">
                            <CountUp
                                :end-val="
                                    unitConversion(metadata.totalCharge, 1000)
                                "
                                :start-val="0"
                                :duration="1.5"
                                :decimalPlaces="2"
                                :options="countUpOptions"
                            ></CountUp>
                            <!-- {{ metadata.totalCharge }} -->
                        </p>
                        <span class="text-center block"
                            >总充电量（{{
                                alternateUnits(metadata.totalCharge, 1000)
                                    ? 'MWh'
                                    : 'kWh'
                            }}）</span
                        >
                    </div>
                    <div>
                        <p class="text-center mb-1">
                            <CountUp
                                :end-val="
                                    unitConversion(
                                        metadata.totalDischarge,
                                        1000
                                    )
                                "
                                :start-val="0"
                                :duration="1.5"
                                :decimalPlaces="2"
                                :options="countUpOptions"
                            ></CountUp>
                            <!-- {{ metadata.totalDischarge }} -->
                        </p>
                        <span class="text-center block"
                            >总放电量（{{
                                alternateUnits(metadata.totalDischarge, 1000)
                                    ? 'MWh'
                                    : 'kWh'
                            }}）</span
                        >
                    </div>
                </div>
            </div>
            <div class="flex justify-center flex-grow">
                <div
                    class="flex flex-col my-mr-5 h-3/4 text-center self-center justify-center"
                >
                    <h4>覆盖城市</h4>
                    <p>单位（个）</p>
                    <span>
                        <CountUp
                            :end-val="metadata.stationCityNum"
                            :start-val="0"
                            :duration="1.5"
                            :options="countUpOptions"
                        ></CountUp>
                        <!-- {{ metadata.stationCityNum }} -->
                    </span>
                </div>
                <div
                    class="flex flex-col h-full my-mr-5 text-center justify-center"
                >
                    <h4>总装机容量</h4>
                    <p>
                        单位（{{
                            alternateUnits(
                                metadata.totalInstalledCapacity,
                                1000
                            )
                                ? 'MWh'
                                : 'kWh'
                        }}）
                    </p>
                    <span>
                        <!-- {{ metadata.totalInstalledCapacity }} -->
                        <CountUp
                            :end-val="
                                unitConversion(
                                    metadata.totalInstalledCapacity,
                                    1000
                                )
                            "
                            :start-val="0"
                            :duration="1.5"
                            :options="countUpOptions"
                        ></CountUp>
                    </span>
                </div>
                <div
                    class="flex flex-col text-center h-3/4 self-center justify-center"
                >
                    <h4>站点总数</h4>
                    <p>单位（个）</p>
                    <span>
                        <!-- {{ metadata.totalStationQuantity }} -->
                        <CountUp
                            :end-val="metadata.totalStationQuantity"
                            :start-val="0"
                            :duration="1.5"
                            :options="countUpOptions"
                        ></CountUp>
                    </span>
                </div>
            </div>
            <div class="flex flex-col">
                <SubTitle class="mb-4" width="82%" :isRight="true"
                    >收益指标</SubTitle
                >
                <div class="flex flex-grow">
                    <div>
                        <p class="text-center mb-1">
                            <CountUp
                                :end-val="
                                    unitConversion(metadata.totalProfit, 10000)
                                "
                                :start-val="0"
                                :duration="1.5"
                                :decimalPlaces="2"
                                :options="countUpOptions"
                            ></CountUp>
                            <!-- {{ metadata.totalProfit }} -->
                        </p>
                        <span class="text-center block"
                            >累计收益（{{
                                alternateUnits(metadata.totalProfit, 10000)
                                    ? '万元'
                                    : '元'
                            }}）</span
                        >
                    </div>
                    <div>
                        <p class="text-center mb-1">
                            <CountUp
                                :end-val="
                                    unitConversion(metadata.todayProfit, 10000)
                                "
                                :start-val="0"
                                :duration="1.5"
                                :decimalPlaces="2"
                                :options="countUpOptions"
                            ></CountUp>
                            <!-- {{ metadata.todayProfit }} -->
                        </p>
                        <span class="text-center block"
                            >今日收益（{{
                                alternateUnits(metadata.todayProfit, 10000)
                                    ? '万元'
                                    : '元'
                            }}）</span
                        >
                    </div>
                </div>
            </div>
        </section>
        <section class="blockContent flex w-full flex-1">
            <div class="flex flex-col">
                <div class="blockChart w-full rounded-lg">
                    <h5 class="w-40 text-base z-50">充放电趋势</h5>
                    <div class="w-full h-full" id="chartDeviceCharge"></div>
                </div>
                <SubTitle class="title-height title2" width="95%"
                    >社会贡献</SubTitle
                >
                <div class="flex flex-col blockCards justify-between gap-y-2">
                    <Lines2Card
                        bgType="1"
                        desc="节约标准煤"
                        :title="social.savingCarbonEmission"
                        :start="socials.savingCarbonEmission"
                    />
                    <Lines2Card
                        bgType="2"
                        desc="CO₂减排量"
                        :title="social.savingCarbonDioxideEmission"
                        :start="socials.savingCarbonDioxideEmission"
                    />
                    <Lines2Card
                        bgType="3"
                        desc="等效植树量"
                        :isComment="false"
                        :title="social.equivalentTreesQuantity"
                        :start="socials.equivalentTreesQuantity"
                    />
                </div>
            </div>
            <div class="flex-grow blockMap mt-5 mb-10" id="chartDeviceMap">
                <chart-viewer
                    class="map-view h-full w-fll"
                    :chart-option="mapOption"
                    height="100%"
                    @optionChange="handleOptChange"
                />
            </div>
            <div class="flex flex-col">
                <div class="blockChart w-full">
                    <h5 class="w-40 text-base z-50">收益趋势</h5>
                    <div class="w-full h-full" id="chartDeviceEarnings"></div>
                </div>
                <SubTitle
                    class="title-height mg-b title2"
                    :isRight="true"
                    width="95%"
                    >收益排行</SubTitle
                >
                <div class="blockEarnings flex flex-col">
                    <ol class="list-none flex flex-col h-full">
                        <li class="flex text-opacity-60 justify-between">
                            <span class="blockEarningsTableTitle">站点</span
                            ><span class="font-normal blockEarningsTableTitle"
                                >收益金额（{{
                                    unitBooan ? '万元' : '元'
                                }}）</span
                            >
                        </li>
                        <ul class="flex flex-col flex-1 ul-box">
                            <li
                                v-for="item in analysises.earningsList"
                                :key="`${item.order}-${item.earnings}`"
                                class="flex justify-between li-box overflow-hidden rounded-sm"
                            >
                                <div
                                    class="relative w-full flex justify-between items-center"
                                    style="padding: 1px"
                                >
                                    <span class="z-20 self-center relative">{{
                                        item.stationName
                                    }}</span>
                                    <span
                                        class="z-20 pr-4 self-center relative"
                                    >
                                        <!-- {{item.profit}} -->
                                        <CountUp
                                            :end-val="item.profit"
                                            :duration="1.5"
                                            :decimalPlaces="2"
                                            :options="countUpOptions"
                                        ></CountUp>
                                    </span>

                                    <div
                                        class="absolute z-10 rounded-sm w-full h-full"
                                        style="left: 1px"
                                    >
                                        <BarProgress
                                            class="absolute z-10 rounded-sm max-w-full"
                                            :style="{
                                                top: '7.5%',
                                                right: '0',
                                                height: '85%',
                                                border: '1px solid #00B6D3',
                                            }"
                                            :ratio="item.ratio"
                                        />
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </ol>
                </div>
            </div>
        </section>
    </div>
</template>
<script>
import * as echarts from 'echarts'
import {
    getStationChargingAndProfitTrend,
    getDeviceMetaData,
    getStationProfitRank,
    getStationChargingAndProfit,
    getSocialContributionDegree,
    getStationProfitTrend,
    getStationChargeTrend,
} from '@/apiService/fullscreen'
// import menuPage from '@/components/menuPage.vue'
import {
    computed,
    onMounted,
    onUnmounted,
    reactive,
    toRefs,
    toRaw,
    ref,
} from 'vue'
// import { useStore } from 'vuex'
import SubTitle from './components/subTitle.vue'
import Lines2Card from './components/lines2Card.vue'
import BarProgress from './components/barProgress.vue'
import ChartViewer from './components/chartViewer.vue'
import { chargeChartOption, earningsChartOption } from './const'
import CountUp from 'vue-countup-v3'
import {
    decorateRankRatioFromSelfList,
    initMap,
    pushInLengthLimitedList,
    countNum,
    //换位置
    genExpectedData,
    genExpectedDatas,
    randomFunction,
} from './utils'
import { unitConversion, alternateUnits, someMax } from '../device/const'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
export default {
    components: { SubTitle, Lines2Card, BarProgress, ChartViewer, CountUp },
    setup() {
        let intervalKey = ``
        let intervalKeyBy5Sec = ``
        let chartDeviceCharge = null
        let chartDeviceEarnings = null
        const route = useRoute()
        const state = reactive({
            mapOption: {},

            metadata: {
                totalCharge: 0,
                totalDischarge: 0,
                stationCityNum: 0,
                totalInstalledCapacity: 0,
                totalStationQuantity: 0,
                totalProfit: 0,
                todayProfit: 0,
                minuteProfitIncrement: 0,
            },
            metadatas: {
                totalCharge: 0,
                totalDischarge: 0,
                stationCityNum: 0,
                totalInstalledCapacity: 0,
                totalStationQuantity: 0,
                totalProfit: 0,
                todayProfit: 0,
                minuteProfitIncrement: 0,
            },
            social: {
                savingCarbonEmission: 0,
                savingCarbonDioxideEmission: 0,
                equivalentTreesQuantity: 0,

                minuteCarbonEmissionIncrement: 0,
                minuteCarbonDioxideEmissionIncrement: 0,
                minuteEquivalentTreesQuantityIncrement: 0,
            },
            socials: {
                savingCarbonEmission: 0,
                savingCarbonDioxideEmission: 0,
                equivalentTreesQuantity: 0,

                minuteCarbonEmissionIncrement: 0,
                minuteCarbonDioxideEmissionIncrement: 0,
                minuteEquivalentTreesQuantityIncrement: 0,
            },
            analysises: {
                chargeAndEarnings: {
                    currentChargeQuantity: [],
                    currentDischargeQuantity: [],
                    currentProfit: [],
                },
            },
        })

        const countUpOptions = {
            useGrouping: false,
        }

        const mapPoints = ref([])
        const fetchDevicMetaData = async () => {
            const [res1, res2] = await Promise.all([
                getDeviceMetaData({ supplierId: route.query.supplierId }),
                getStationChargingAndProfit({
                    supplierId: route.query.supplierId,
                }),
            ])
            state.metadatas = {
                ...state.metadata,
            }
            state.metadata = {
                ...state.metadata,
                ...res1?.data?.data,
                ...res2?.data?.data,
            }

            const mapPoint =
                state?.metadata?.stationCoordinateList.map((item) => [
                    item.longitude,
                    item.latitude,
                    item.province,
                ]) || []
            mapPoints.value = mapPoint
            state.mapOption.series[1].data = mapPoint
        }

        const fetchDevicSocial = async () => {
            const { data } = await getSocialContributionDegree({
                supplierId: route.query.supplierId,
            })
            state.socials = { ...state.social }
            state.social = { ...state.social, ...data.data }
        }

        const fetchDeviceChargeAndEarningsChart = async () => {
            const {
                data: { code, data },
            } = await getStationChargingAndProfitTrend({
                supplierId: route.query.supplierId,
            })

            if (code === 0) {
                state.analysises.chargeAndEarnings = {
                    ...data,
                }
            }
        }

        const unitBooan = ref(false)
        const fetchDeviceEarningsList = async () => {
            const {
                data: { data, code },
            } = await getStationProfitRank({
                supplierId: route.query.supplierId,
            })
            if (code === 0) {
                const arr = data.map((item) => item.profit)
                const isBooan = someMax(arr, 10000)
                unitBooan.value = isBooan
                if (isBooan) {
                    data?.forEach((item) => {
                        item.profit = item?.profit
                            ? (item.profit / 10000).toFixed(2)
                            : 0
                    })
                }

                state.analysises.earningsList = decorateRankRatioFromSelfList(
                    data,
                    'profit'
                )
            }
        }

        const batchRenderChart = () => {
            const $chartDeviceChart =
                document.getElementById('chartDeviceCharge')
            const $chartDeviceEarnings = document.getElementById(
                'chartDeviceEarnings'
            )
            chartDeviceCharge = echarts.init($chartDeviceChart)
            chartDeviceEarnings = echarts.init($chartDeviceEarnings)
            chartDeviceCharge.setOption(chargeChartOption)
            chartDeviceEarnings.setOption(earningsChartOption)
            window.addEventListener('resize', resizeEcharts)
        }

        const resizeEcharts = () => {
            chartDeviceEarnings.resize()
            chartDeviceCharge.resize()
        }

        const averageChargeQuantity = ref(0)
        // const averageDischargeQuantity = ref(0)
        const averageProfit = ref(0)
        const averageChargeQuantityArr = ref([])
        // const averageDischargeQuantityArr = ref([])
        const averageProfitArr = ref([])
        const initAnalysisCharts = async (boolean) => {
            try {
                await fetchDeviceChargeAndEarningsChart()

                averageChargeQuantityArr.value = [0, 14, 28, 42]
                averageProfitArr.value = [0, 14, 28, 42]
                const averageStart =
                    averageChargeQuantityArr?.value[
                        averageChargeQuantityArr.value.length - 1
                    ] || 0
                const averageEnd =
                    averageChargeQuantityArr?.value[
                        averageChargeQuantityArr.value.length - 2
                    ] || 0
                averageChargeQuantity.value =
                    (averageStart - averageEnd) / 10 / 12
                const profitStart =
                    averageProfitArr?.value[
                        averageProfitArr.value.length - 1
                    ] || 0
                const profitEnd =
                    averageProfitArr?.value[
                        averageProfitArr.value.length - 2
                    ] || 0
                averageProfit.value = (profitStart - profitEnd) / 10 / 12
                echartsData()
            } catch (error) {
                //
            }
        }

        const echartsData = () => {
            averageChargeQuantityArr.value.push(
                randomFunction(
                    averageChargeQuantityArr.value[
                        averageChargeQuantityArr.value.length - 1
                    ],
                    averageChargeQuantity.value
                )
            )
            averageProfitArr.value.push(
                randomFunction(
                    averageProfitArr.value[averageProfitArr.value.length - 1],
                    averageProfit.value
                )
            )
            chartDeviceCharge.setOption({
                series: [{ data: averageChargeQuantityArr.value }],
            })
            chartDeviceEarnings.setOption({
                series: [{ data: averageProfitArr.value }],
            })
        }

        const handleOptChange = (opt) => {
            //
        }

        const getStationProfitTrendData = async () => {
            const {
                data: { data, code },
            } = await getStationChargingAndProfitTrend({
                supplierId: route.query.supplierId,
            })
            if (code === 0) {
                const timeData = data.map((item) =>
                    dayjs(item.date).format('MM/DD')
                )
                const charge = data.map((item) => {
                    return item.charge
                })
                const discharge = data.map((item) => {
                    return item.discharge
                })
                const profit = data.map((item) => {
                    return item.profit
                })
                earningsChartOption.xAxis.data = timeData
                chargeChartOption.xAxis.data = timeData
                chargeChartOption.series[0].data = charge
                chargeChartOption.series[1].data = discharge
                earningsChartOption.series[0].data = profit
                chartDeviceCharge.setOption(chargeChartOption)
                chartDeviceEarnings.setOption(earningsChartOption)
            }
        }

        const getTwoEchart = () => {
            getStationProfitTrendData()
        }

        const echartsAdd = ref(false)
        let timeInterval = null
        // const adaptors = ref(void 0)
        onMounted(() => {
            batchRenderChart()
            function loop() {
                state.metadatas.todayProfit = state.metadata.todayProfit
                state.metadata.todayProfit = countNum(
                    state.metadata.todayProfit,
                    state.metadata.minuteProfitIncrement / 12
                )
                state.socials = { ...state.social }
                state.social = {
                    ...state.social,
                    savingCarbonEmission: genExpectedData(
                        state.social.savingCarbonEmission,
                        state.social.minuteCarbonEmissionIncrement / 12,
                        2
                    ),
                    savingCarbonDioxideEmission: genExpectedData(
                        state.social.savingCarbonDioxideEmission,
                        state.social.minuteCarbonDioxideEmissionIncrement / 12,
                        2
                    ),
                    equivalentTreesQuantity: genExpectedData(
                        state.social.equivalentTreesQuantity,
                        state.social.minuteEquivalentTreesQuantityIncrement /
                            12,
                        2
                    ),
                }
                state.analysises.chargeAndEarnings = {
                    currentChargeQuantity: genExpectedData(
                        toRaw(
                            state.analysises.chargeAndEarnings
                                .currentChargeQuantity
                        ),
                        0,
                        1,
                        true,
                        -30
                    ),
                    currentDischargeQuantity: genExpectedData(
                        toRaw(
                            state.analysises.chargeAndEarnings
                                .currentDischargeQuantity
                        ),
                        0,
                        1,
                        true,
                        -30
                    ),
                    currentProfit: genExpectedData(
                        toRaw(state.analysises.chargeAndEarnings.currentProfit),
                        0,
                        1,
                        true,
                        -30
                    ),
                }
            }
            async function start() {
                // clearInterval(intervalKeyBy5Sec)
                fetchDevicMetaData()
                fetchDevicSocial()
                fetchDeviceEarningsList()
                // initAnalysisCharts(echartsAdd.value)
                getTwoEchart()
                intervalKeyBy5Sec = setInterval(loop, 5 * 1000)
            }
            start()
            state.mapOption = initMap()
            intervalKey = setInterval(() => {
                echartsAdd.value = true
                start()
                intervalKeyBy5Sec && clearInterval(intervalKeyBy5Sec)
            }, 60 * 1000)

            timeInterval = setInterval(getTwoEchart, 1000 * 60 * 60)
        })

        onUnmounted(() => {
            clearInterval(intervalKey)
            clearInterval(intervalKeyBy5Sec)
            clearInterval(timeInterval)
            window.removeEventListener('resize', resizeEcharts)
        })

        return {
            ...toRefs(state),
            handleOptChange,
            countUpOptions,
            unitConversion,
            alternateUnits,
            unitBooan,
        }
    },
}
</script>
<style lang="less" scoped>
.viewScreen {
    // overflow: hidden;
    position: fixed;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0;
    background-color: #001733;
    z-index: 100;
    padding: 0 44px 36px 44px;

    & > .h2 {
        padding-top: 12px;
        font-size: 24px;
        font-family: AlibabaPuHuiTi_2_85_Bold;
        color: #a6f9ff;
        // line-height: 65px;
        // height: 65px;
        // background: #001733 url(../../assets/fullScreen/device/title.png)
        //     no-repeat center center / 100%;
        // background-image: url(../../assets/fullScreen/bgAll/bg-6.png);
        // background-repeat: no-repeat;
        // background-position: center center;
        padding-bottom: 24px;
    }

    .blockMeta {
        & > div {
            // height: 19vh;
            height: 208px;
        }

        & > *:nth-child(1),
        & > *:nth-child(3) {
            flex-basis: 28.5%;

            & > div {
                & > div {
                    flex-basis: 50%;
                    // #001733
                    background: // url(../../assets/fullScreen/device/index-left.png)
                        url(../../assets/fullScreen/bgAll/bg-3.png) no-repeat
                        center center / 100%;
                }

                p {
                    height: 36px;
                    font-size: 32px;
                    font-family: WeChat-Sans-Std, WeChat-Sans-Std;
                    font-weight: bold;
                    color: #ffffff;
                    line-height: 36px;
                    background: linear-gradient(
                        180deg,
                        #acf5ff 0%,
                        #d8d8d8 100%
                    );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                span {
                    height: 22px;
                    font-size: 16px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: #a6f9ff;
                    line-height: 22px;
                }
            }
        }

        & > *:nth-child(3) {
            & > div {
                & > div {
                    // #001733
                    background: // url(../../assets/fullScreen/device/index-right.png)
                        url(../../assets/fullScreen/bgAll/bg-1.png) no-repeat
                        center center / 100%;
                }
            }
        }

        //

        & > *:nth-child(2) {
            & > *:nth-child(1),
            & > *:nth-child(3) {
                width: 160px;
                height: 172px !important;
                // #001733
                background: url(../../assets/fullScreen/bgAll/bg-5.png)
                    no-repeat center center / 100%;
                background-size: 100% 100%;
            }

            & > *:nth-child(2) {
                width: 240px;
                // #001733
                background: url(../../assets/fullScreen/bgAll/bg-4.png)
                    no-repeat center center / 100%;

                background-size: 100% 100%;
            }

            h4 {
                font-size: 24px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #a6f9ff;
                line-height: 32px;
            }
            p {
                height: 17px;
                font-size: 12px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #a6f9ff;
                line-height: 17px;
            }
            span {
                height: 75px;
                font-size: 68px;
                font-family: WeChat-Sans-Std, WeChat-Sans-Std;
                font-weight: bold;
                color: #ffffff;
                line-height: 75px;
            }
        }

        .box-pt {
            padding-top: 0.375rem;
        }
    }

    .blockContent {
        // height: calc(~'81vh - 116px - 16px - 32px');
        margin-top: 10px;
        & > *:nth-child(1),
        & > *:nth-child(3) {
            width: 23vw;

            .blockChart {
                height: 28vh;
                position: relative;
                background: rgba(0, 36, 71, 0.1);
                border-radius: 8px;
                // overflow: hidden;
                background: #001733;
                // border: 1px solid;
                // border-image: linear-gradient(
                //         139deg,
                //         rgba(0, 220, 235, 1),
                //         rgba(0, 52, 95, 1)
                //     )
                //     1 1;
                // backdrop-filter: blur(10px);
                &::after {
                    display: block;
                    content: '';
                    width: calc(~'100% + 2px');
                    height: calc(~'100% + 2px');
                    position: absolute;
                    left: -1px;
                    top: -1px;
                    border-radius: 8px;
                    background-image: linear-gradient(
                        139deg,
                        rgba(0, 220, 235, 1),
                        rgba(0, 52, 95, 1)
                    );
                    z-index: -1;
                }

                & > h5 {
                    text-indent: 15px;
                    height: 32px;
                    line-height: 32px !important;
                    position: absolute;
                    top: 0;
                    left: 0;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: #c5e4e9;
                    font-size: 18px !important;

                    background: rgba(0, 36, 71, 0.1)
                        url(../../assets/fullScreen/device/title-mark.png)
                        no-repeat center center / 100%;
                }
            }
        }
        & > *:nth-child(3) {
        }

        .flex-mt {
            margin-top: 0.375rem;
        }
    }

    .blockCards {
        height: 25.5vh;
        .blockCard {
            flex: 1;
        }
    }

    .blockEarnings {
        height: 25.5vh;

        ol {
            li {
                position: relative;
                color: #ffffff;
                text-indent: 7px;
                // padding-top: 3.5px;
                // padding-bottom: 3.5px;

                // & > *:nth-child(1) {
                //     font-size: 14px;
                //     font-family: AlibabaPuHuiTi_2_55_Regular;
                //     color: #ffffff;
                //     text-indent: 7px;
                // }
                // & > *:nth-child(2) {
                //     font-size: 18px;
                //     font-family: WeChat-Sans-Std-Medium, WeChat-Sans-Std;
                //     font-weight: 500;
                // }

                .blockEarningsTableTitle {
                    font-size: 0.75rem;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: rgba(255, 255, 255, 0.65);
                }
            }

            .ul-box {
                > li {
                    margin-top: 8px;
                    background: linear-gradient(
                        270deg,
                        #005a9b 0%,
                        #001733 100%
                    );
                    border-radius: 0.25rem;
                    border: 1px solid;
                    border-image: linear-gradient(
                            139deg,
                            rgba(0, 52, 95, 1),
                            rgba(0, 220, 235, 1)
                        )
                        1 1;
                }

                .li-box {
                    height: 36px;
                    box-sizing: border-box;
                }
            }
        }
    }

    :deep(.countup-wrap) {
        font-family: AlibabaPuHuiTi_2_55_Regular !important;
    }

    .blockMeta {
        padding-top: 0 !important;
    }

    .mb-8 {
        margin-bottom: 33px !important;
    }

    .titleSub {
        font-size: 24px !important;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #a6f9ff;
        height: 45px;
        line-height: 32px;
    }

    .my-mr-5 {
        margin-right: 20px;
    }

    .mt-5 {
        margin-top: 21px !important;
    }

    .title-height {
        height: 45px;
        margin-top: 30px;
        margin-bottom: 10px;
    }

    .mg-b {
        margin-bottom: 20px;
    }

    .mb-10 {
        margin-bottom: 10px !important;
    }
}
@media screen and (max-width: 1600px) {
    .viewScreen > .h2 {
        padding-top: 8px;
        padding-bottom: 10px;
    }
    .viewScreen .titleSub {
        margin-bottom: 10px;
        // margin-top: 20px;
        font-size: 20px !important;
    }
    .viewScreen .title2 {
        margin-top: 20px;
    }
    .viewScreen .blockMeta > *:nth-child(2) > *:nth-child(1),
    .viewScreen .blockMeta > *:nth-child(2) > *:nth-child(3) {
        width: 150px;
        height: 152px !important;
    }
    .viewScreen .blockMeta > *:nth-child(2) > *:nth-child(2) {
        width: 200px;
        height: 172px !important;
    }
    .viewScreen .blockMeta > *:nth-child(2) span {
        font-size: 58px;
        line-height: 62px;
        height: 62px;
    }
    .viewScreen .blockMeta > div {
        height: 172px;
    }
    .viewScreen .blockContent > *:nth-child(1) .blockChart > h5,
    .viewScreen .blockContent > *:nth-child(3) .blockChart > h5 {
        width: 130px;
        height: 26px;
        line-height: 26px !important;
        font-size: 16px !important;
    }
    .blockCard {
        padding: 6px 0 6px 28px;
    }
    .title-header {
        height: 72px;
        img {
            max-height: 100%;
        }
    }
    .viewScreen .blockEarnings ol .ul-box > li {
        margin-top: 7px;
    }
}
</style>
