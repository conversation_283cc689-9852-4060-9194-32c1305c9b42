<template>
    <div
        :class="`${$props.class} ${bgClass} blockCard flex flex-col justify-center`"
    >
        <h4 class="font-bold">
            <CountUp
                :start-val="kgUnitg(start)"
                :end-val="kgUnitg(title)"
                :duration="1.5"
                :decimalPlaces="2"
                :options="countUpOptions"
            ></CountUp>
            <!-- {{ title }} -->
        </h4>
        <p>{{ desc }}（{{ isComment ? isUnits(title) : '棵' }}）</p>
    </div>
</template>
<script>
import { computed } from 'vue'
import CountUp from 'vue-countup-v3'
import {
    kgUnitg,
    isUnits,
    unitConversion,
    alternateUnits,
} from '@/views/device/const'
export const Lines2Card = {
    name: `Lines2Card`,
    components: { CountUp },
    props: {
        class: {
            type: String,
            default: '',
        },
        bgType: {
            validator: (v) => +v === 1 || +v === 2 || +v === 3,
            default: 1,
        },
        desc: [String, Number],
        title: [String, Number],
        start: [String, Number],
        isComment: {
            type: Boolean,
            default: true,
        },
        // background: String,
    },
    setup(props) {
        const bgClass = computed(() => {
            return `bg${props.bgType}`
        })

        const countUpOptions = {
            useGrouping: false,
        }
        return {
            bgClass,
            countUpOptions,
            unitConversion,
            alternateUnits,
            kgUnitg,
            isUnits,
        }
    },
}
export default Lines2Card
</script>
<style lang="less" scoped>
.blockCard {
    padding: 10px 0px 10px 40px;
    // border: 1px solid;
    // border-image: linear-gradient(
    //         139deg,
    //         rgba(0, 220, 235, 1),
    //         rgba(0, 52, 95, 1)
    //     )
    //     1 1;
    background: #001733 url(../../../assets/fullScreen/device/tag-1.png)
        no-repeat center center;
    background-size: 100% 100%;
    h4 {
        font-size: 32px;
        font-family: WeChat-Sans-Std, WeChat-Sans-Std;
        font-weight: bold;
        color: #f7fbff;
        line-height: 40px;
    }
    p {
        height: 20px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #f7fbff;
        line-height: 20px;
    }

    &.bg1 {
        background-image: url('../../../assets/fullScreen/bgAll/bg-9.png');
    }
    &.bg2 {
        background-image: url('../../../assets/fullScreen/bgAll/bg-8.png');
    }
    &.bg3 {
        background-image: url('../../../assets/fullScreen/bgAll/bg-7.png');
    }

    :deep(.countup-wrap) {
        font-family: AlibabaPuHuiTi_2_55_Regular !important;
    }
}
</style>
