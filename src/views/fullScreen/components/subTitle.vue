<template>
    <h3
        :class="
            `titleSub ${isRight ? 'titleSubRight text-right ' : ' '} ${
                $props.class
            }`
        "
        :style="width ? `width: ${width};` : ''"
    >
        <slot name="default" />
    </h3>
</template>
<script>
export const SubTitle = {
    name: `SubTitle`,
    props: {
        class: {
            type: String,
            default: '',
        },
        width: String,
        isRight: Boolean,
    },
}
export default SubTitle
</script>
<style lang="less" scoped>
.titleSub {
    text-indent: 22%;
    font-size: 1.125rem;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #a6f9ff;
    line-height: 2;
    padding: 6px 0;
    background: #001733
        url(../../../assets/fullScreen/device/title-sub-left.png) no-repeat
        center center / 100%;

    &.titleSubRight {
        text-indent: 0;
        padding-right: 20.3%;
        // background: #001733
        //     url(../../../assets/fullScreen/device/title-sub-right.png) no-repeat
        //     center center / 100%;
        //     margin-left: auto;
        background-image: url(../../../assets/fullScreen/bgAll/bg-2.png);
        background-repeat: no-repeat;
        background-position: center center;
        margin-left: auto;
    }
}
</style>
