<template>
    <div class="qr-redirect-container">
        <div class="loading-card">
            <div class="spinner" v-if="isLoading"></div>
            <div class="icon success" v-else-if="redirectStatus === 'success'">
                ✓
            </div>
            <div class="icon error" v-else-if="redirectStatus === 'error'">
                ✗
            </div>

            <h2>{{ title }}</h2>
            <p class="message">{{ message }}</p>

            <div class="environment-info" v-if="showDebugInfo">
                <h4>环境检测信息</h4>
                <ul>
                    <li>是否微信环境: {{ envInfo.isWechat ? '是' : '否' }}</li>
                    <li>是否移动端: {{ envInfo.isMobile ? '是' : '否' }}</li>
                    <li>
                        是否小程序: {{ envInfo.isMiniProgram ? '是' : '否' }}
                    </li>
                    <li>用户代理: {{ envInfo.userAgent }}</li>
                </ul>
            </div>

            <div class="actions" v-if="showActions">
                <button @click="retryRedirect" class="btn primary">重试</button>
                <button @click="manualRedirect" class="btn secondary">
                    手动跳转
                </button>
                <button @click="toggleDebugInfo" class="btn text">
                    {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
    smartRedirect,
    isWechat,
    isMobile,
    isMiniProgram,
} from '@/utils/qrCodeRedirect'

const route = useRoute()

// 响应式数据
const isLoading = ref(true)
const redirectStatus = ref('')
const title = ref('正在跳转...')
const message = ref('检测环境中，请稍候...')
const showActions = ref(false)
const showDebugInfo = ref(false)

// 环境信息
const envInfo = ref({
    isWechat: false,
    isMobile: false,
    isMiniProgram: false,
    userAgent: '',
})

// 配置选项
const redirectOptions = ref({
    miniProgramAppId: 'your-miniprogram-appid', // 替换为实际的小程序AppId
    h5BaseUrl: window.location.origin, // 当前域名
})

// 获取URL参数
const sn = route.query.sn || ''

// 检测环境信息
const detectEnvironment = async () => {
    envInfo.value = {
        isWechat: isWechat(),
        isMobile: isMobile(),
        isMiniProgram: await isMiniProgram(),
        userAgent: navigator.userAgent,
    }
}

// 执行跳转
const performRedirect = async () => {
    if (!sn) {
        redirectStatus.value = 'error'
        title.value = '参数错误'
        message.value = '缺少设备编号参数'
        showActions.value = true
        isLoading.value = false
        return
    }

    try {
        isLoading.value = true
        redirectStatus.value = ''
        title.value = '正在跳转...'

        // 根据环境显示不同的提示信息
        if (envInfo.value.isMiniProgram) {
            message.value = '检测到小程序环境，正在跳转...'
        } else if (envInfo.value.isWechat) {
            message.value = '检测到微信环境，正在打开小程序...'
        } else {
            message.value = '正在跳转到H5页面...'
        }

        // 执行智能跳转
        await smartRedirect(sn, redirectOptions.value)

        // 如果3秒后还没跳转成功，显示手动选项
        setTimeout(() => {
            if (isLoading.value) {
                redirectStatus.value = 'error'
                title.value = '跳转超时'
                message.value = '自动跳转失败，请选择手动跳转方式'
                showActions.value = true
                isLoading.value = false
            }
        }, 3000)
    } catch (error) {
        console.error('跳转失败:', error)
        redirectStatus.value = 'error'
        title.value = '跳转失败'
        message.value = error.message || '跳转过程中发生错误'
        showActions.value = true
        isLoading.value = false
    }
}

// 重试跳转
const retryRedirect = () => {
    showActions.value = false
    performRedirect()
}

// 手动跳转
const manualRedirect = () => {
    const h5Url = `${redirectOptions.value.h5BaseUrl}/h5?sn=${sn}`

    if (envInfo.value.isWechat) {
        // 微信环境，提示用户搜索小程序
        alert(
            `请在微信中搜索小程序，或者复制以下链接在浏览器中打开：\n${h5Url}`
        )
    } else {
        // 直接跳转到H5页面
        window.open(h5Url, '_blank')
    }
}

// 切换调试信息显示
const toggleDebugInfo = () => {
    showDebugInfo.value = !showDebugInfo.value
}

// 页面挂载时执行
onMounted(async () => {
    console.log('二维码跳转页面加载，设备编号:', sn)

    // 检测环境
    await detectEnvironment()

    // 延迟1秒后执行跳转，让用户看到加载状态
    setTimeout(() => {
        performRedirect()
    }, 1000)
})
</script>

<style lang="less" scoped>
.qr-redirect-container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        sans-serif;
}

.loading-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 100%;

    h2 {
        margin: 20px 0 10px;
        color: #333;
        font-size: 24px;
        font-weight: 600;
    }

    .message {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 30px;
    }
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #3edacd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
    font-weight: bold;
    color: white;

    &.success {
        background: #4caf50;
    }

    &.error {
        background: #f44336;
    }
}

.environment-info {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;

    h4 {
        margin: 0 0 15px;
        color: #333;
        font-size: 16px;
    }

    ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
            padding: 5px 0;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;

        &.primary {
            background: #3edacd;
            color: white;

            &:hover {
                background: #35c5b8;
            }
        }

        &.secondary {
            background: #f5f5f5;
            color: #333;

            &:hover {
                background: #e0e0e0;
            }
        }

        &.text {
            background: transparent;
            color: #666;
            font-size: 14px;

            &:hover {
                color: #3edacd;
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .qr-redirect-container {
        padding: 10px;
    }

    .loading-card {
        padding: 30px 20px;

        h2 {
            font-size: 20px;
        }

        .message {
            font-size: 14px;
        }
    }
}
</style>
