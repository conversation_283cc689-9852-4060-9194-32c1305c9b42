/**
 * 二维码扫码跳转工具
 * 根据扫码环境自动跳转到对应页面
 */

/**
 * 检测是否为微信环境
 * @returns {boolean}
 */
export function isWechat() {
    try {
        if (typeof navigator === 'undefined') return false
        const ua = navigator.userAgent.toLowerCase()
        return /micromessenger/.test(ua)
    } catch (error) {
        console.warn('检测微信环境时出错:', error)
        return false
    }
}

/**
 * 检测是否为移动端
 * @returns {boolean}
 */
export function isMobile() {
    try {
        if (typeof navigator === 'undefined') return false
        const ua = navigator.userAgent.toLowerCase()
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(ua)
    } catch (error) {
        console.warn('检测移动端环境时出错:', error)
        return false
    }
}

/**
 * 检测是否为微信小程序环境
 * @returns {Promise<boolean>}
 */
export function isMiniProgram() {
    return new Promise((resolve) => {
        try {
            // 检查是否在浏览器环境中
            if (typeof window === 'undefined') {
                resolve(false)
                return
            }

            // 检查wx对象是否存在
            if (typeof window.wx !== 'undefined' && window.wx.miniProgram) {
                window.wx.miniProgram.getEnv((res) => {
                    resolve(res.miniprogram || false)
                })
            } else {
                resolve(false)
            }
        } catch (error) {
            console.warn('检测小程序环境时出错:', error)
            resolve(false)
        }
    })
}

/**
 * 跳转到微信小程序
 * @param {string} sn - 设备编号
 * @param {string} appId - 小程序AppId（可选）
 */
export function redirectToMiniProgram(sn, appId) {
    const path = `packageWeb/index?sn=${sn}`

    try {
        // 检查是否在微信小程序环境中
        if (typeof window !== 'undefined' && typeof window.wx !== 'undefined' && window.wx.miniProgram) {
            // 如果在微信小程序环境中，直接跳转
            window.wx.miniProgram.navigateTo({
                url: `/${path}`,
                success: () => {
                    console.log('跳转到小程序页面成功')
                },
                fail: (err) => {
                    console.error('跳转到小程序页面失败:', err)
                }
            })
        } else if (isWechat()) {
            // 如果在微信浏览器中，尝试打开小程序
            if (appId) {
                // 如果有AppId，使用微信开放标签
                const openTag = `
                    <wx-open-launch-weapp
                        id="launch-btn-${Date.now()}"
                        appid="${appId}"
                        path="${path}"
                        style="display: none;"
                    >
                        <template>
                            <button>打开小程序</button>
                        </template>
                    </wx-open-launch-weapp>
                `
                document.body.insertAdjacentHTML('beforeend', openTag)

                // 自动触发点击
                setTimeout(() => {
                    const launchBtn = document.querySelector(`#launch-btn-${Date.now()}`)
                    if (launchBtn) {
                        launchBtn.click()
                    }
                }, 100)
            } else {
                // 降级方案：提示用户手动打开小程序
                alert('请在微信中搜索小程序并打开对应页面')
            }
        } else {
            console.warn('当前环境不支持跳转到微信小程序')
            // 降级到H5页面
            redirectToH5(sn)
        }
    } catch (error) {
        console.error('跳转到小程序时出错:', error)
        // 降级到H5页面
        redirectToH5(sn)
    }
}

/**
 * 跳转到H5页面
 * @param {string} sn - 设备编号
 * @param {string} baseUrl - 基础URL（可选）
 */
export function redirectToH5(sn, baseUrl = '') {
    const url = `${baseUrl}/#/h5?sn=${sn}`

    if (isMobile()) {
        // 移动端直接跳转
        window.location.href = url
    } else {
        // 桌面端在新窗口打开
        window.open(url, '_blank')
    }
}

/**
 * 智能跳转函数
 * 根据环境自动选择跳转方式
 * @param {string} sn - 设备编号
 * @param {Object} options - 配置选项
 * @param {string} options.miniProgramAppId - 小程序AppId
 * @param {string} options.h5BaseUrl - H5基础URL
 */
export async function smartRedirect(sn, options = {}) {
    const { miniProgramAppId, h5BaseUrl = '' } = options

    console.log('开始智能跳转，设备编号:', sn)
    console.log('用户代理:', navigator.userAgent)

    try {
        // 检测是否在小程序环境中
        const inMiniProgram = await isMiniProgram()

        if (inMiniProgram) {
            console.log('检测到小程序环境，跳转到小程序页面')
            redirectToMiniProgram(sn, miniProgramAppId)
            return
        }

        // 检测是否为微信环境
        if (isWechat()) {
            console.log('检测到微信环境，尝试跳转到小程序')
            redirectToMiniProgram(sn, miniProgramAppId)
            return
        }

        // 其他情况跳转到H5页面
        console.log('跳转到H5页面')
        redirectToH5(sn, h5BaseUrl)

    } catch (error) {
        console.error('智能跳转失败:', error)
        // 降级到H5页面
        redirectToH5(sn, h5BaseUrl)
    }
}

/**
 * 创建二维码跳转页面
 * @param {string} sn - 设备编号
 * @param {Object} options - 配置选项
 */
export function createQRRedirectPage(sn, options = {}) {
    const {
        miniProgramAppId,
        h5BaseUrl = '',
        title = '正在跳转...',
        loadingText = '检测环境中，请稍候...'
    } = options

    // 创建加载页面
    const loadingHTML = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    background: #f5f5f5;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                .loading-container {
                    text-align: center;
                    padding: 40px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }
                .spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #e3e3e3;
                    border-top: 4px solid #3EDACD;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .loading-text {
                    color: #666;
                    font-size: 16px;
                }
            </style>
        </head>
        <body>
            <div class="loading-container">
                <div class="spinner"></div>
                <div class="loading-text">${loadingText}</div>
            </div>
            <script>
                // 这里会插入跳转逻辑
                setTimeout(() => {
                    // 执行智能跳转
                    window.smartRedirect('${sn}', ${JSON.stringify(options)});
                }, 1000);
            </script>
        </body>
        </html>
    `

    return loadingHTML
}

// 默认导出
export default {
    isWechat,
    isMobile,
    isMiniProgram,
    redirectToMiniProgram,
    redirectToH5,
    smartRedirect,
    createQRRedirectPage
}
