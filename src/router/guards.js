export async function beforeEachGuard(to, from, next) {
    try {
        await store.commit('user/setMenu')

        // 检查是否在白名单中
        if (isRouteInWhitelist(to.path)) {
            console.log(`路由 ${to.path} 在白名单中，跳过token验证`)
            next()
            return
        }

        // 获取token和用户信息
        const token = Cookies.get('token')

        // 如果没有token，重定向到登录页
        if (!token) {
            console.log('未找到token，重定向到登录页')
            handleNoToken(to, next)
            return
        }

        // 有token的情况下，验证用户信息
        await handleWithToken(to, userInfoData, next)

    } catch (error) {
        console.error('路由守卫出错:', error)
        handleGuardError(to, error, next)
    }
}