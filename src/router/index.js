import { createRouter, createWebHashHistory } from 'vue-router'
// import service from '@/apiService'
// import { message } from 'ant-design-vue'

export const routes = [
    {
        path: '/device',
        name: 'layout',
        breadcrumbName: 'layout',
        component: () => import('@/views/layout.vue'),
        redirect: '/device',
        children: [
            {
                path: '/device/alarmOverview',
                name: 'alarmOverview',
                component: () => import('@/views/device/alarmOverview.vue'),
            },

            {
                path: '/rolePage',
                name: '系统设置',
                component: () => import('@/views/role/index.vue'),
            },
            {
                path: '/operation',
                name: 'operation',
                component: () => import('@/views/operation/index.vue'),
            },
            {
                path: '/operation-car',
                name: 'operationCar',
                component: () => import('@/views/device/car/operation/index.vue'),
                redirect: '/operation-car/project',
                children: [
                    {
                        path: '/operation-car/device',
                        name: 'operationCarDevice',
                        component: () => import('@/views/device/car/operation/device.vue'),
                    },
                    {
                        path: '/operation-car/project',
                        name: 'operationCarProject',
                        component: () => import('@/views/device/car/operation/project.vue'),
                    },
                    {
                        path: '/operation-car/alarm',
                        name: 'operationCarAlarm',
                        component: () => import('@/views/device/car/operation/alarm.vue'),
                    },
                ]
            },
            {
                path: '/user',
                name: 'user',
                component: () => import('@/views/user/index.vue'),
            },
            {
                path: '/device',
                name: 'deviceOverview',
                component: () => import('@/views/device/siteOverview.vue'),
                meta: {
                    keepAlive: true,
                    isFullPage: true
                }
            },
            {
                path: '/device/deviceDetail',
                name: 'deviceDetail',
                component: () => import('@/views/device/deviceDetail.vue'),
            },
            {
                path: '/car',
                name: 'carOverview',
                component: () => import('@/views/device/car/siteOverviewCar.vue'),
                meta: {
                    keepAlive: true,
                    isFullPage: true
                }
            },
            {
                path: '/carSystem/detail',
                name: 'carSystemDetail',
                component: () => import('@/views/device/car/detail.vue'),
            },
            {

                path: '/carSystem/equipmentDetail',
                name: 'equipmentDetail',
                component: () => import('@/views/device/car/equipmentDetail.vue'),
            }
        ],
    },
    {
        path: '/strategy',
        name: 'strategy',
        component: () => import('@/views/strategy/index.vue'),
        redirect: '/strategy/energy',
        children: [
            {
                path: '/strategy/energy',
                name: 'energy',
                component: () => import('@/views/strategy/energy.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/price',
                name: 'price',
                component: () => import('@/views/strategy/price/index.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/ring',
                name: 'ring',
                component: () => import('@/views/strategy/ring/index.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/models',
                name: 'models',
                component: () => import('@/views/strategy/models.vue'),
            },
        ]
    },
    {
        path: '/fullScreen/device',
        name: 'fullScreenDevice',
        component: () => import('@/views/fullScreen/device.vue'),
    },
    {
        path: '/packageWeb/index',
        name: 'packageWeb',
        component: () => import('@/views/packageWeb/index.vue'),
    },
    {
        path: '/h5',
        name: 'h5',
        component: () => import('@/views/packageWeb/h5-index.vue'),
        meta: {
            title: '设备详情-H5版本',
        },
    },
    {
        path: '/packageWeb/demo',
        name: 'packageWebDemo',
        component: () => import('@/views/packageWeb/demo.vue'),
        meta: {
            title: '小程序转H5演示',
        },
    },
    {
        path: '/packageWeb/alarm',
        name: 'alarm',
        component: () => import('@/views/packageWeb/alarm.vue'),
        meta: {
            title: '告警信息',
        },
    },
    {
        path: '/qr-redirect',
        name: 'qrRedirect',
        component: () => import('@/views/qrRedirect.vue'),
        meta: {
            title: '二维码跳转',
        },
    },
    // {
    //     path: '/h5',
    //     name: 'h5',
    //     component: () => import('@/views/packageWeb/index.vue'),
    //     meta: {
    //         title: 'H5页面',
    //     },
    // },
    {
        path: '/qr-example',
        name: 'qrExample',
        component: () => import('@/views/qrCodeExample.vue'),
        meta: {
            title: '二维码跳转示例',
        },
    },
    {
        path: '/login',
        component: () => import('@/views/auth/loginAndRegister.vue'),
        meta: {
            title: '登录',
            dontNeedBuy: true,
            headerBg: '#fff',
        },
        children: [
            {
                path: '',
                name: 'login',
                component: () => import('@/views/auth/login.vue'),
            },

        ],
    },

    {
        path: '/agreement',
        name: 'agreement',
        meta: {
            dontNeedBuy: true,
        },
        component: () => import('@/views/auth/agreement.vue'),
    },
    {
        path: "/merchant-agreement",
        name: "merchant-agreement",
        component: () => import("@/views/agreement/merchant.vue"),
        meta: {
            dontNeedBuy: true,
        },
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'noFind',
    },
]
const permissionRoutes = {
    //设置
    ES_SETTING_MANAGER: [

    ],
    //渠道
    ES_AUTHORIZATION_MANAGER: [

    ],
    //订单管理
    ES_ORDER_MANAGER: [

    ],
    //商品管理
    ES_GOODS_MANAGER: [

    ],
    //服务管理
    ES_SERVICE_MANAGER: [

    ],
    ES_COOPERATEORDER_MANAGER: [

    ],
    // 文章管理
    ES_ARTICLE_MANAGER: [

    ],
    //客户管理
    ES_CUSTOMER_MANAGER: [

    ],
    //营销管理
    ES_MARKETING_MANAGER: [

    ],
    ES_CASE_MANAGER: [

    ],
}
const router = createRouter({
    history: createWebHashHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        return { x: 0, y: 0 }
    },
})
export const permissionRouter = (merchantEditionType, roleCodes) => {
    let menuKeys = []
    if (merchantEditionType == 'PERSONAL') {
        //如果是个人版，除了设置菜单其他都有
        menuKeys = Object.keys(permissionRoutes).filter(
            (p) => p != 'ES_SETTING_MANAGER'
        )
    } else if (merchantEditionType == 'ENTERPRISE') {
        menuKeys =
            roleCodes.indexOf('ES_SUPPLIER_MANAGER') >= 0
                ? Object.keys(permissionRoutes)
                : roleCodes
    }

    menuKeys.forEach((key) => {
        permissionRoutes[key].forEach((item) => {
            router.addRoute('layout', item)
        })
    })
}
//先不做动态路由了，刷新的时候有问题
Object.keys(permissionRoutes).forEach((key) => {
    permissionRoutes[key].forEach((item) => {
        router.addRoute('layout', item)
    })
})

// 导入路由白名单配置
import { getWhitelistConfig } from './whitelist.js'

// 打印当前白名单配置（开发环境）
if (process.env.NODE_ENV === 'development') {
    console.log('当前路由白名单配置:', getWhitelistConfig())
}

// 导入路由守卫
import { beforeEachGuard, afterEachGuard, routeErrorHandler } from './guards.js'

// 设置路由守卫
router.beforeEach(beforeEachGuard)
router.afterEach(afterEachGuard)
router.onError(routeErrorHandler)


export default router
