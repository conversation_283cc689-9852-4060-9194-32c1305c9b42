import api from '@/api'
import { Get, Post } from '@/common/request'
function pushLockCommand(obj) {
    return Post({
        url: api.pushLockCommand,
        bodyParam: obj
    })
}
function getDevicePageList(params) {
  return Post({
      url: api.getDevicePageList,
      bodyParam: params
  })
}
function getProjectPageList(params) {
  return Post({
      url: api.getProjectPageList,
      bodyParam: params
  })
}


function getBasicInfo (param){
    return Get({
        url: api.getBasicInfo,
        urlParam: param
    })
}
function getRealData (param){
    return Get({
        url: api.getRealData,
        urlParam: param
    })
}
function getRealCells (param){
    return Get({
        url: api.getRealCells,
        urlParam: param
    })
}
function getBmsDataColumn (param){
    return Get({
        url: api.getBmsDataColumn,
        urlParam: param
    })
}
function getBmsDataLog (obj){
    return Post({
        url: api.getBmsDataLog,
        bodyParam: obj
    })
}

function getBmsDataPage (obj){
    return Post({
        url: api.getBmsDataPage,
        bodyParam: obj
    })
}


function statsActiveDeviceCount (param){
    return Get({
        url: api.statsActiveDeviceCount,
        urlParam: param
    })
}
function projectDetail (param){
    return Get({
        url: api.projectDetail,
        urlParam: param
    })
}

function statsPowerBattUsageSummary (obj){
    return Post({
        url: api.statsPowerBattUsageSummary,
        bodyParam: obj
    })
}


function statsDevicesStatusCount (param){
    return Get({
        url: api.statsDevicesStatusCount,
        urlParam: param
    })
}
function statsDeviceSocProportion (param){
    return Get({
        url: api.statsDeviceSocProportion,
        urlParam: param
    })
}
function statsPowerBattDailyUsage (obj){
    return Post({
        url: api.statsPowerBattDailyUsage,
        bodyParam: obj
    })
}
function getPowerBmsChargeRecordPageList (obj){
    return Post({
        url: api.getPowerBmsChargeRecordPageList,
        bodyParam: obj
    })
}
function createProject (obj){
    return Post({
        url: api.createProject,
        bodyParam: obj
    })
}
function editProject (obj){
    return Post({
        url: api.editProject,
        bodyParam: obj
    })
}
function getProjectAndBatteryCounts (param){
    return Get({
        url: api.getProjectAndBatteryCounts,
        urlParam: param
    })
}
function batchAddBattery (obj){
    return Post({
        url: api.batchAddBattery,
        bodyParam: obj
    })
}
function batchImportBattery (obj){
    return Post({
        url: api.batchImportBattery,
        bodyParam: obj,
        config: {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          },
    })
}
function getCellRangeAndVariance (param){
    return Get({
        url: api.getCellRangeAndVariance,
        urlParam: param
    })
}

function getStatsDeviceSocAndWorkStatusByDay (param){
    return Get({
        url: api.getStatsDeviceSocAndWorkStatusByDay,
        urlParam: param
    })
}
function getSupplierBmsSummary (param){
    return Get({
        url: api.getSupplierBmsSummary,
        urlParam: param
    })
}
function statsPowerBattDurUsageSummary (obj){
    return Post({
        url: api.statsPowerBattDurUsageSummary,
        bodyParam: obj
    })
}
function statsDeviceChargeRank (obj){
    return Post({
        url: api.statsDeviceChargeRank,
        bodyParam: obj
    })
}
function statisticDeviceUseTimeDistribution (param){
    return Get({
        url: api.statisticDeviceUseTimeDistribution,
        urlParam: param
    })
}
function statisticDeviceRunTimeDistribution (param){
    return Get({
        url: api.statisticDeviceRunTimeDistribution,
        urlParam: param
    })
}

function getStatisticalCard (obj){
    return Post({
        url: api.powerGetStatisticalCard,
        bodyParam: obj
    })
}

function getStatisticalSummary (obj){
    return Post({
        url: api.powerGetStatisticalSummary,
        bodyParam: obj
    })
}
function powerDeviceAlarmPage (obj){
    return Post({
        url: api.powerDeviceAlarmPage,
        bodyParam: obj
    })
}
function powerDeviceAlarmDetail (param){
    return Get({
        url: api.powerDeviceAlarmDetail,
        urlParam: param
    })
}

function addDictItem (obj){
    return Post({
        url: api.addDictItem,
        urlParam:obj,
        // bodyParam: obj,
    })
}
function statsDeviceChargeTimeDistribution (param){
    return Get({
        url: api.statsDeviceChargeTimeDistribution,
        urlParam: param
    })
}

function statsDeviceChargeStartSocDistribution (param){
    return Get({
        url: api.statsDeviceChargeStartSocDistribution,
        urlParam: param
    })
}

function statsDeviceChargeCapacityDistribution (param){
    return Get({
        url: api.statsDeviceChargeCapacityDistribution,
        urlParam: param
    })
}
function getReginDeviceTree (param){
    return Get({
        url: api.getReginDeviceTree,
        urlParam: param
    })
}

export default {
    statsDeviceChargeCapacityDistribution,
    statsDeviceChargeStartSocDistribution,
    statsDeviceChargeTimeDistribution,
    pushLockCommand,
    getDevicePageList,
    getProjectPageList,
    getBasicInfo,
    getRealData,
    getRealCells,
    getBmsDataColumn,
    getBmsDataLog,
    getBmsDataPage,
    statsActiveDeviceCount,
    projectDetail,
    statsPowerBattUsageSummary,
    statsDevicesStatusCount,
    statsDeviceSocProportion,
    statsPowerBattDailyUsage,
    getPowerBmsChargeRecordPageList,
    createProject,
    editProject,
    getProjectAndBatteryCounts,
    batchAddBattery,
    batchImportBattery,
    getCellRangeAndVariance,
    getStatsDeviceSocAndWorkStatusByDay,
    getSupplierBmsSummary,
    statsPowerBattDurUsageSummary,
    statsDeviceChargeRank,
    statisticDeviceUseTimeDistribution,
    statisticDeviceRunTimeDistribution,
    getStatisticalCard,
    getStatisticalSummary,
    powerDeviceAlarmPage,
    powerDeviceAlarmDetail,
    addDictItem,
    getReginDeviceTree,
}